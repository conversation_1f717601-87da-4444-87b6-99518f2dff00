# Work Finder

A job search website built with <PERSON><PERSON>, Vite, and Ant Design.

## Features

- User authentication (login, register, forgot password)
- Dashboard with job statistics
- Job search and filtering
- User profile management
- Responsive design

## Tech Stack

- React 18
- Vite
- TypeScript
- Ant Design
- Redux Toolkit
- React Router DOM
- React Hook Form
- Yup for validation
- Axios for API calls

## Getting Started

### Prerequisites

- Node.js (v16+)
- npm or yarn

### Installation

1. Clone the repository
2. Install dependencies:

```bash
npm install
# or
yarn
```

3. Start the development server:

```bash
npm run dev
# or
yarn dev
```

4. Open your browser and navigate to `http://localhost:5173`

## Project Structure

```
src/
  ├── app/           # App configuration (Redux store)
  ├── assets/        # Static assets (images, fonts, etc.)
  ├── components/    # Reusable components
  ├── constants/     # Constants and configuration
  ├── containers/    # Container components
  ├── contexts/      # React contexts
  ├── hooks/         # Custom hooks
  ├── layout/        # Layout components
  ├── menu-items/    # Menu configuration
  ├── pages/         # Page components
  ├── services/      # API services
  ├── store/         # Redux store and slices
  ├── themes/        # Theme configuration
  ├── types/         # TypeScript types
  ├── utils/         # Utility functions
  ├── App.tsx        # Main App component
  ├── Routes.tsx     # Route configuration
  └── main.tsx       # Entry point
```

## License

This project is licensed under the MIT License.
