{"name": "work-finder", "version": "0.1.0", "private": true, "dependencies": {"@date-io/date-fns": "^2.16.0", "@emotion/cache": "^11.10.3", "@emotion/react": "^11.10.4", "@emotion/styled": "^11.10.4", "@hookform/resolvers": "^2.9.7", "@mui/icons-material": "^5.10.6", "@mui/lab": "^5.0.0-alpha.98", "@mui/material": "^5.10.4", "@mui/system": "^5.10.4", "@mui/utils": "^5.10.3", "@mui/x-data-grid": "^5.17.1", "@mui/x-date-pickers": "^5.0.0", "@reduxjs/toolkit": "^1.8.5", "apexcharts": "^4.0.0", "axios": "^0.27.2", "axios-mock-adapter": "^1.21.2", "copy-to-clipboard": "^3.3.3", "crypto-js": "^4.1.1", "csstype": "^3.1.0", "date-fns": "^2.29.3", "file-saver": "^2.0.5", "firebase": "^9.9.4", "framer-motion": "^7.2.1", "lodash": "^4.17.21", "material-ui-popup-state": "^4.0.2", "moment": "^2.29.4", "qs": "^6.11.0", "react": "^18.2.0", "react-apexcharts": "^1.4.0", "react-beautiful-dnd": "^13.1.1", "react-beautiful-dnd-grid": "^0.1.3-alpha", "react-dnd": "^16.0.1", "react-dnd-html5-backend": "^16.0.1", "react-dom": "^18.2.0", "react-hook-form": "^7.34.2", "react-intl": "^6.1.1", "react-multi-email": "^1.0.9", "react-number-format": "^5.1.3", "react-perfect-scrollbar": "^1.5.8", "react-redux": "^8.0.2", "react-router-dom": "^6.3.0", "react-slick": "^0.29.0", "react-timer-hook": "^3.0.5", "react18-input-otp": "^1.1.0", "redux": "^4.2.0", "redux-persist": "^6.0.0", "redux-saga": "^1.2.2", "remark-gfm": "^3.0.1", "stylis-plugin-rtl": "^2.1.1", "typescript": "^4.8.2", "web-vitals": "^3.0.1", "yup": "^0.32.11"}, "scripts": {"dev": "vite", "build": "tsc && vite build", "lint": "eslint --ext .ts,.tsx ./src", "preview": "vite preview"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@types/crypto-js": "^4.1.1", "@types/file-saver": "^2.0.7", "@types/lodash": "^4.17.7", "@types/node": "^20.11.24", "@types/react": "^18.0.20", "@types/react-beautiful-dnd": "^13.1.8", "@types/react-dom": "^18.0.5", "@types/react-redux": "^7.1.24", "@types/react-router-dom": "^5.3.3", "@types/react-slick": "^0.23.10", "@typescript-eslint/eslint-plugin": "^5.36.2", "@typescript-eslint/parser": "^5.36.2", "@vitejs/plugin-react": "^4.0.0", "eslint": "^8.19.0", "eslint-config-airbnb-typescript": "^17.0.0", "eslint-config-prettier": "^8.5.0", "eslint-import-resolver-typescript": "^3.5.0", "eslint-plugin-import": "^2.26.0", "eslint-plugin-jsx-a11y": "^6.6.1", "eslint-plugin-prettier": "^4.2.1", "eslint-plugin-react": "^7.30.1", "eslint-plugin-react-hooks": "^4.6.0", "prettier": "^2.7.1", "sass": "^1.55.0", "typescript": "^5.2.2", "vite": "^4.4.5"}}