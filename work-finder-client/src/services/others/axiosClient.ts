import axios, { AxiosRequestConfig, AxiosResponse } from 'axios';

import { syncUrl } from 'index';

const axiosClient = axios.create({
    headers: {
        'Content-Type': 'application/json'
    }
});

// Add a request interceptor
axiosClient.interceptors.request.use(
    function (config: AxiosRequestConfig) {
        return { ...config, baseURL: syncUrl };
    },
    function (error) {
        return Promise.reject(error);
    }
);

// Add a response interceptor
axiosClient.interceptors.response.use(
    function (response: AxiosResponse) {
        return response.data;
    },
    function (error) {
        return Promise.reject(error);
    }
);

export default axiosClient;
