import { createAsyncThunk, createSlice } from '@reduxjs/toolkit';
import omit from 'lodash/omit';

import { IOption, IPaginationParam, IResponseList, ITimeSheetConfigList, Response } from 'types';
import { INonBillablesConfig, INonBillablesConfigResponse } from 'types/non-billables-config';
import sendRequest from 'services/ApiService';
import { RootState } from 'app/store';
import Api from 'constants/Api';

export const getNonbillableConfig = createAsyncThunk<IResponseList<INonBillablesConfigResponse>, IPaginationParam>(
    Api.nonBillableConfig.search.url,
    async (params) => {
        const response = await sendRequest(Api.nonBillableConfig.search, params);
        return response;
    }
);

export const getTimesheetStatus = createAsyncThunk<IResponseList<ITimeSheetConfigList>, { color?: boolean }>(
    `${Api.non_billable_monitoring.findAllConfig}`,
    async () => {
        const response = await sendRequest(Api.non_billable_monitoring.findAllConfig);

        return response;
    }
);

export const editNonbillableConfig = createAsyncThunk<Response<{ content: any }>, INonBillablesConfig>(
    'Api.nonBillableConfig.edit',
    async (params) => {
        const response = await sendRequest(Api.nonBillableConfig.edit(params.id), omit(params, ['id']));

        return response;
    }
);

interface INonBillConfigState {
    config?: IResponseList<INonBillablesConfigResponse>['result'];
    loading: { [key: string]: boolean };
    timesheetStatusOptions: IOption[];
}

const initialState: INonBillConfigState = {
    loading: {},
    timesheetStatusOptions: []
};

const nonbillableConfigSlice = createSlice({
    name: 'non-billable-config',
    initialState: initialState,
    reducers: {},
    extraReducers: (builder) => {
        builder.addCase(getNonbillableConfig.pending, (state, action) => {
            state.loading[getNonbillableConfig.typePrefix] = true;
        });
        builder.addCase(getNonbillableConfig.fulfilled, (state, action) => {
            if (action.payload.status && Array.isArray(action.payload.result.content)) {
                state.config = action.payload.result;
            }
            state.loading[getNonbillableConfig.typePrefix] = false;
        });
        builder.addCase(getNonbillableConfig.rejected, (state) => {
            state.loading[getNonbillableConfig.typePrefix] = false;
        });
        builder.addCase(editNonbillableConfig.pending, (state) => {
            state.loading[editNonbillableConfig.typePrefix] = true;
        });
        builder.addCase(editNonbillableConfig.fulfilled, (state) => {
            state.loading[editNonbillableConfig.typePrefix] = false;
        });
        builder.addCase(editNonbillableConfig.rejected, (state) => {
            state.loading[editNonbillableConfig.typePrefix] = false;
        });
        builder.addCase(getTimesheetStatus.pending, (state) => {
            state.loading[getTimesheetStatus.typePrefix] = true;
        });
        builder.addCase(getTimesheetStatus.fulfilled, (state, action) => {
            state.loading[getTimesheetStatus.typePrefix] = false;
            if (action.payload?.status) {
                state.timesheetStatusOptions = action.payload.result.content.map((status) => ({
                    value: status.key,
                    label: status.name,
                    typeCode: status.typeCode,
                    color: action.meta.arg.color ? status.color : undefined
                }));
            }
        });
        builder.addCase(getTimesheetStatus.rejected, (state) => {
            state.loading[getTimesheetStatus.typePrefix] = false;
        });
    }
});

export default nonbillableConfigSlice.reducer;

export const nonBIllableConfigSelector = (state: RootState) => state.config;
