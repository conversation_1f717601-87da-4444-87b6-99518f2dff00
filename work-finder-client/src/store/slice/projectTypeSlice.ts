import { createAsyncThunk, createSlice } from '@reduxjs/toolkit';
import omit from 'lodash/omit';

import { ICreateProjectTypeRequest, IEditProjectTypeRequest, IGetProjectTypeResponse } from 'types/projectType';
import { IProjectTypeFilterConfig } from 'pages/administration/Config';
import { IResponseList, Response } from 'types';
import sendRequest from 'services/ApiService';
import { RootState } from 'app/store';
import Api from 'constants/Api';

export const getSearchProjectType = createAsyncThunk<IResponseList<IGetProjectTypeResponse>, IProjectTypeFilterConfig>(
    Api.project.typeConfig.search.url,
    async (params) => {
        const response = await sendRequest(Api.project.typeConfig.search, params);

        return response;
    }
);

export const createProjectType = createAsyncThunk<Response<{ content: string }>, ICreateProjectTypeRequest>(
    Api.project.typeConfig.create.url,
    async (params) => {
        const response = await sendRequest(Api.project.typeConfig.create, params);

        return response;
    }
);

export const editProjectType = createAsyncThunk<Response<{ content: string }>, IEditProjectTypeRequest>(
    'Api.project.typeConfig.edit.url',
    async (params) => {
        const response = await sendRequest(Api.project.typeConfig.edit(params.id), omit(params, ['id']));

        return response;
    }
);

export const deleteProjectType = createAsyncThunk<Response<{ content: string }>, string>(
    'Api.project.typeConfig.delete.url',
    async (params) => {
        const response = await sendRequest(Api.project.typeConfig.delete(params));

        return response;
    }
);

interface IProjectTypeState {
    projectTypes?: IResponseList<IGetProjectTypeResponse>['result'];
    loading: { [key: string]: boolean };
}

const initialState: IProjectTypeState = {
    loading: {}
};

const projectTypeSlice = createSlice({
    name: 'project-type',
    initialState: initialState,
    reducers: {},
    extraReducers: (builder) => {
        builder.addCase(getSearchProjectType.pending, (state) => {
            state.loading[getSearchProjectType.typePrefix] = true;
        });
        builder.addCase(getSearchProjectType.fulfilled, (state, action) => {
            if (action.payload.status && Array.isArray(action.payload.result.content)) {
                state.projectTypes = action.payload.result;
            }
            state.loading[getSearchProjectType.typePrefix] = false;
        });
        builder.addCase(getSearchProjectType.rejected, (state) => {
            state.loading[getSearchProjectType.typePrefix] = false;
        });
        builder.addCase(createProjectType.pending, (state) => {
            state.loading[createProjectType.typePrefix] = true;
        });
        builder.addCase(createProjectType.fulfilled, (state) => {
            state.loading[createProjectType.typePrefix] = false;
        });
        builder.addCase(createProjectType.rejected, (state) => {
            state.loading[createProjectType.typePrefix] = false;
        });
        builder.addCase(editProjectType.pending, (state) => {
            state.loading[editProjectType.typePrefix] = true;
        });
        builder.addCase(editProjectType.fulfilled, (state) => {
            state.loading[editProjectType.typePrefix] = false;
        });
        builder.addCase(editProjectType.rejected, (state) => {
            state.loading[editProjectType.typePrefix] = false;
        });
        builder.addCase(deleteProjectType.pending, (state) => {
            state.loading[deleteProjectType.typePrefix] = true;
        });
        builder.addCase(deleteProjectType.fulfilled, (state) => {
            state.loading[deleteProjectType.typePrefix] = false;
        });
        builder.addCase(deleteProjectType.rejected, (state) => {
            state.loading[deleteProjectType.typePrefix] = false;
        });
    }
});

export default projectTypeSlice.reducer;

export const projectTypeSelector = (state: RootState) => state.projectType;
