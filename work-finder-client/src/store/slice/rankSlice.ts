// redux
import { createAsyncThunk, createSlice } from '@reduxjs/toolkit';
import { RootState } from 'app/store';

// project imports
import { paginationResponseDefault } from 'constants/Common';
import { IGetAllRankResponse, IPaginationParam, IPaginationResponse, IRank, IResponseList } from 'types';
import sendRequest from 'services/ApiService';
import Api from 'constants/Api';

// interface
interface IRankInitialState {
    rank: IRank[];
    pagination: IPaginationResponse;
    loading: {
        [key: string]: boolean;
    };
}

// initialState
const initialState: IRankInitialState = {
    rank: [],
    pagination: paginationResponseDefault,
    loading: {}
};

// Call API
export const getAllRank = createAsyncThunk<IResponseList<IGetAllRankResponse>, IPaginationParam>(Api.rank.getAll.url, async (params) => {
    const response = await sendRequest(Api.rank.getAll, params);
    return response;
});

// Slice & Actions
const rankSlice = createSlice({
    name: 'rank',
    initialState: initialState,
    reducers: {},
    extraReducers: (builder) => {
        // getAll
        builder.addCase(getAllRank.pending, (state) => {
            state.rank = [];
            state.loading[getAllRank.typePrefix] = true;
        });
        builder.addCase(getAllRank.fulfilled, (state, action) => {
            if (action.payload?.status) {
                const { pagination, content } = action.payload.result;
                state.pagination = pagination;
                state.rank = content;
            }
            state.loading[getAllRank.typePrefix] = false;
        });
        builder.addCase(getAllRank.rejected, (state) => {
            state.loading[getAllRank.typePrefix] = false;
        });
    }
});

// Reducer & export
export default rankSlice.reducer;

// export const {} = rankSlice.actions;

// Selector & export
export const rankSelector = (state: RootState) => state.rank;
