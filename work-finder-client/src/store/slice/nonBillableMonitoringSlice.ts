import { createAsyncThunk, createSlice } from '@reduxjs/toolkit';

import { GetNonBillByMemberRequest, INonBillableMonitoringResponse, IResponseList, IWarningNonbillMemberResponse } from 'types';
import sendRequest from 'services/ApiService';
import { RootState } from 'app/store';
import Api from 'constants/Api';

// interface
interface INonBillableMonitoringState {
    loading: { [key: string]: boolean };
    NBMByMember?: IResponseList<INonBillableMonitoringResponse>['result']['content'];
    warningNBMByMember: IResponseList<IWarningNonbillMemberResponse>['result']['content'];
}

// initial state
const initialState: INonBillableMonitoringState = {
    loading: {},
    warningNBMByMember: []
};

export const getNBMByMember = createAsyncThunk<IResponseList<INonBillableMonitoringResponse>, GetNonBillByMemberRequest>(
    Api.non_billable_monitoring.getAll.url,
    async (params) => {
        const response = await sendRequest(Api.non_billable_monitoring.getAll, params);

        return response;
    }
);

export const getWarningNBMBytMember = createAsyncThunk<IResponseList<IWarningNonbillMemberResponse>, GetNonBillByMemberRequest>(
    Api.non_billable_monitoring.getWarningNonbillable.url,
    async (params) => {
        const response = await sendRequest(Api.non_billable_monitoring.getWarningNonbillable, params);

        return response;
    }
);

const nonBillableMonitoringSlice = createSlice({
    name: 'non-billable-monitoring',
    initialState: initialState,
    reducers: {},
    extraReducers: (builder) => {
        // getNBMByMember
        builder.addCase(getNBMByMember.pending, (state) => {
            state.NBMByMember = undefined;
            state.loading[getNBMByMember.typePrefix] = true;
        });
        builder.addCase(getNBMByMember.fulfilled, (state, action) => {
            state.loading[getNBMByMember.typePrefix] = false;
            if (action.payload.status) {
                state.NBMByMember = action.payload.result.content;
            }
        });
        builder.addCase(getNBMByMember.rejected, (state) => {
            state.loading[getNBMByMember.typePrefix] = false;
        });

        // getWarningNBMBytMember
        builder.addCase(getWarningNBMBytMember.pending, (state) => {
            state.warningNBMByMember = [];
            state.loading[getWarningNBMBytMember.typePrefix] = true;
        });
        builder.addCase(getWarningNBMBytMember.fulfilled, (state, action) => {
            state.loading[getWarningNBMBytMember.typePrefix] = false;
            if (action.payload.status) {
                state.warningNBMByMember = action.payload.result.content;
            }
        });
        builder.addCase(getWarningNBMBytMember.rejected, (state) => {
            state.loading[getWarningNBMBytMember.typePrefix] = false;
        });
    }
});

// export const {} = nonBillableMonitoringSlice.actions;

// selectors
export const nonBillableMonitoringSelector = (state: RootState) => state.nonBillableMonitoring;

// reducers
export default nonBillableMonitoringSlice.reducer;
