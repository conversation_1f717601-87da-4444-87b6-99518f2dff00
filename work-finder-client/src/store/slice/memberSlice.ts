import { createAsyncThunk, createSlice } from '@reduxjs/toolkit';

import { IResetPasswordRequest } from 'types/member';
import sendRequest from 'services/ApiService';
import { RootState } from 'app/store';
import { Response } from 'types';
import Api from 'constants/Api';

// interface
interface IMemberState {
    loading: { [key: string]: boolean };
}

// initial state
const initialState: IMemberState = {
    loading: {}
};

export const resetPassowrd = createAsyncThunk<Response<any>, IResetPasswordRequest>('Api.member.resetPassowrd.url', async (params) => {
    const response = await sendRequest(Api.member.resetPassowrd(params.userId));

    return response;
});

const memberSlice = createSlice({
    name: 'member',
    initialState: initialState,
    reducers: {},
    extraReducers: (builder) => {
        builder.addCase(resetPassowrd.pending, (state) => {
            state.loading[resetPassowrd.typePrefix] = true;
        });
        builder.addCase(resetPassowrd.fulfilled, (state, action) => {
            state.loading[resetPassowrd.typePrefix] = false;
        });
        builder.addCase(resetPassowrd.rejected, (state) => {
            state.loading[resetPassowrd.typePrefix] = false;
        });
    }
});

// export const { } = memberSlice.actions;

// selectors
export const memberSelector = (state: RootState) => state.member;

// reducers
export default memberSlice.reducer;
