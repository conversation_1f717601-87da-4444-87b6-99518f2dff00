import { createAsyncThunk, createSlice } from '@reduxjs/toolkit';

import { IOption, IProjectList, IResponseList } from 'types';
import sendRequest from 'services/ApiService';
import { RootState } from 'app/store';
import Api from 'constants/Api';

// interface
interface IProjectOptions extends IOption {
    billable?: string;
    dept?: string;
    projectType?: string;
}
interface IWeeklyEffortState {
    loading: { [key: string]: boolean };
    projectOptions: IProjectOptions[];
}

// initial state
const initialState: IWeeklyEffortState = {
    loading: {},
    projectOptions: []
};
export const getProjectAllForOption = createAsyncThunk<
    IResponseList<IProjectList>,
    {
        type: 'week' | 'month';
        value: { fromDate: string; toDate: string };
        dept?: string;
        projectType?: string;
        billable?: string;
        color?: boolean;
    }
>('monthly-effort/getProjectAllForOption', async (params) => {
    const response = await sendRequest(Api.master.getProjectAllWithoutFixcost, {
        ...params.value,
        dept: params.dept,
        projectType: params.projectType,
        billable: params.billable,
        projectAuthorization: true,
        size: 1000
    });

    return response;
});

const monthlyEffortSlice = createSlice({
    name: 'monthly-effort',
    initialState: initialState,
    reducers: {},
    extraReducers: (builder) => {
        // getProjectAllForOption
        builder.addCase(getProjectAllForOption.pending, (state) => {
            state.projectOptions = [];
            state.loading[getProjectAllForOption.typePrefix] = true;
        });
        builder.addCase(getProjectAllForOption.fulfilled, (state, action) => {
            state.loading[getProjectAllForOption.typePrefix] = false;
            if (action.payload?.status) {
                state.projectOptions = action.payload.result.content.map((prj) => ({
                    value: prj.projectId,
                    label: prj.projectName,
                    typeCode: prj.typeCode,
                    billable: prj.billable,
                    dept: prj.deptId,
                    projectType: prj.type,
                    color: action.meta.arg.color ? prj.effortArise : undefined
                }));
            }
        });
        builder.addCase(getProjectAllForOption.rejected, (state) => {
            state.loading[getProjectAllForOption.typePrefix] = false;
        });
    }
});

// selectors
export const monthlyEffortSelector = (state: RootState) => state.monthlyEffort;

// reducers
export default monthlyEffortSlice.reducer;
