import omit from 'lodash/omit';

// redux
import { createAsyncThunk, createSlice } from '@reduxjs/toolkit';
import { RootState } from 'app/store';

// // project imports
import { clearAllCookies, setCookieByKeyObject } from 'utils/cookies';
import { ESTATUS_LOGIN } from 'constants/Common';
import sendRequest from 'services/ApiService';
import { IForgotConfig } from 'pages/Config';
import { Response } from 'types';
import Api from 'constants/Api';
import {
    IGetPackageAccountResponse,
    IChangePasswordRequest,
    ILoginRequest,
    ILoginResponse,
    IPackageAccount,
    IUserInfo,
    IRegisterRequest,
    IVerifyEmailRequest,
    IVerifyEmailResponse,
    ICreatePasswordRequest,
    ICreatePasswordResponse
} from 'types/authentication';

// interface
interface IAuthState {
    status: ESTATUS_LOGIN;
    loading: { [key: string]: boolean };
    packageAccount: IPackageAccount[];
    isAccountExpired: boolean;
    showLoadingScreen: boolean;
    registerSuccessfully: boolean;
    forgotPWSuccessfully: boolean;
    createPasswordSuccessfully: boolean;
    verifyTokenStatus?: 'PENDING' | 'VERIFIED' | 'CREATED';
    userInfo?: IUserInfo;
}

// initial state
const initialState: IAuthState = {
    status: ESTATUS_LOGIN.NOT_YET,
    loading: {},
    packageAccount: [],
    isAccountExpired: false,
    showLoadingScreen: true,
    registerSuccessfully: false,
    forgotPWSuccessfully: false,
    createPasswordSuccessfully: false
};

export const changePassword = createAsyncThunk<Response<any>, IChangePasswordRequest>('Api.auth.changePassword.url', async (params) => {
    const response = await sendRequest(Api.auth.changePassword(params.userId), omit(params, ['userId']));

    return response;
});

export const createPassword = createAsyncThunk<Response<ICreatePasswordResponse>, ICreatePasswordRequest>(
    Api.auth.createPassword.url,
    async (params) => {
        const response = await sendRequest(Api.auth.createPassword, params);

        return response;
    }
);

export const loginRequest = createAsyncThunk<Response<ILoginResponse>, ILoginRequest>('Api.auth.login.url', async (params) => {
    const data: Response<ILoginResponse> = await sendRequest(Api.auth.login(params.ldap), omit(params, ['ldap']));

    return data;
});

export const getUserInfo = createAsyncThunk<Response<{ content: IUserInfo }>, { showLoadingScreen?: boolean }>(
    'Api.auth.me.url',
    async (_, { dispatch, rejectWithValue }) => {
        try {
            const response = await sendRequest(Api.auth.me);

            if (!response.status) {
                dispatch(logout());
                if (response.result?.content?.code === 400 && response.result?.content?.message === 'Please renew your package') {
                    dispatch(setExpiredScreen(true));
                    return;
                }
                window.location.reload();
                return;
            }

            return response;
        } catch (error) {
            dispatch(logout());
            return rejectWithValue(error);
        }
    }
);

export const verifyToken = createAsyncThunk<Response<IVerifyEmailResponse>, IVerifyEmailRequest>(
    Api.auth.verifyToken.url,
    async (params) => {
        const data = await sendRequest(Api.auth.verifyToken, params);

        return data;
    }
);

export const getAllPackageAccount = createAsyncThunk<Response<IGetPackageAccountResponse>>(Api.auth.getAllpackageAccount.url, async () => {
    const data = await sendRequest(Api.auth.getAllpackageAccount);

    return data;
});

export const register = createAsyncThunk<Response<any>, IRegisterRequest>(Api.auth.register.url, async (params) => {
    const data = await sendRequest(Api.auth.register, params);

    return data;
});

export const forgotPW = createAsyncThunk<Response<any>, IForgotConfig>('Api.auth.forgot.url', async (params) => {
    const data = await sendRequest(Api.auth.forgot(params.email));

    return data;
});

const authSlice = createSlice({
    name: 'auth',
    initialState: initialState,
    reducers: {
        logout(state) {
            clearAllCookies();
            localStorage.clear();
            state.isAccountExpired = false;
            state.status = ESTATUS_LOGIN.NOT_YET;
        },
        resetAuthState(state) {
            state.registerSuccessfully = false;
            state.createPasswordSuccessfully = false;
            state.forgotPWSuccessfully = false;
            state.verifyTokenStatus = undefined;
        },
        setExpiredScreen(state, action) {
            state.isAccountExpired = action.payload;
        }
    },
    extraReducers: (builder) => {
        builder.addCase(loginRequest.pending, (state) => {
            state.loading[loginRequest.typePrefix] = true;
        });
        builder.addCase(loginRequest.fulfilled, (state) => {
            state.loading[loginRequest.typePrefix] = false;
        });
        builder.addCase(loginRequest.rejected, (state) => {
            state.loading[loginRequest.typePrefix] = false;
        });
        builder.addCase(getUserInfo.pending, (state, action) => {
            state.showLoadingScreen = action.meta.arg.showLoadingScreen === undefined ? true : action.meta.arg.showLoadingScreen;
            state.loading[getUserInfo.typePrefix] = true;
        });
        builder.addCase(getUserInfo.fulfilled, (state, action) => {
            state.loading[getUserInfo.typePrefix] = false;
            if (action.payload?.status) {
                const { content } = action.payload.result;
                state.status = ESTATUS_LOGIN.SUCCESS;
                state.userInfo = content;
                setCookieByKeyObject({ isLDAP: content.isLdap });
            }
        });
        builder.addCase(getUserInfo.rejected, (state) => {
            state.loading[getUserInfo.typePrefix] = false;
        });
        builder.addCase(changePassword.pending, (state) => {
            state.loading[changePassword.typePrefix] = true;
        });
        builder.addCase(changePassword.fulfilled, (state) => {
            state.loading[changePassword.typePrefix] = false;
        });
        builder.addCase(changePassword.rejected, (state) => {
            state.loading[changePassword.typePrefix] = false;
        });
        builder.addCase(getAllPackageAccount.pending, (state) => {
            state.loading[getAllPackageAccount.typePrefix] = true;
        });
        builder.addCase(getAllPackageAccount.fulfilled, (state, action) => {
            if (action.payload?.status) {
                state.packageAccount = action.payload.result.content;
            }
            state.loading[getAllPackageAccount.typePrefix] = false;
        });
        builder.addCase(getAllPackageAccount.rejected, (state) => {
            state.loading[getAllPackageAccount.typePrefix] = false;
        });
        builder.addCase(register.pending, (state) => {
            state.loading[register.typePrefix] = true;
        });
        builder.addCase(register.fulfilled, (state, action) => {
            if (action.payload?.status) {
                state.registerSuccessfully = true;
            }
            state.loading[register.typePrefix] = false;
        });
        builder.addCase(register.rejected, (state) => {
            state.loading[register.typePrefix] = false;
        });
        builder.addCase(verifyToken.pending, (state) => {
            state.loading[verifyToken.typePrefix] = true;
        });
        builder.addCase(verifyToken.fulfilled, (state, action) => {
            if (action.payload?.status && action.payload.result.content.statusVerify) {
                state.verifyTokenStatus = action.payload.result.content.statusVerify;
            }
            state.loading[verifyToken.typePrefix] = false;
        });
        builder.addCase(verifyToken.rejected, (state) => {
            state.loading[verifyToken.typePrefix] = false;
        });
        builder.addCase(createPassword.pending, (state) => {
            state.loading[createPassword.typePrefix] = true;
        });
        builder.addCase(createPassword.fulfilled, (state, action) => {
            if (action.payload?.status) {
                state.createPasswordSuccessfully = true;
            }
            state.loading[createPassword.typePrefix] = false;
        });
        builder.addCase(createPassword.rejected, (state) => {
            state.loading[createPassword.typePrefix] = false;
        });
        builder.addCase(forgotPW.pending, (state) => {
            state.loading[forgotPW.typePrefix] = true;
        });
        builder.addCase(forgotPW.fulfilled, (state, action) => {
            if (action.payload?.status) {
                state.forgotPWSuccessfully = true;
            }
            state.loading[forgotPW.typePrefix] = false;
        });
        builder.addCase(forgotPW.rejected, (state) => {
            state.loading[forgotPW.typePrefix] = false;
        });
    }
});

export const { logout, resetAuthState, setExpiredScreen } = authSlice.actions;

// selectors
export const authSelector = (state: RootState) => state.auth;

// reducers
export default authSlice.reducer;
