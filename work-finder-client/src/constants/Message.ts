export const VALIDATE_MESSAGES = {
    REQUIRED: 'required',
    INVALID_NUMBER: 'invalid-number',
    POSITIVE_NUMBER: 'positive-number',
    LESS_OR_EQUAL: 'less-or-equal',
    LARGER_OR_EQUAL: 'larger-or-equal',
    TRIM: 'trim',
    ONE_DECIMAL: 'one-decimal',
    OUTBOARDDATE: 'outboarddate',
    ENDDATE: 'enddate',
    AFTER_DAY: 'after-day',
    DATE_FORMAT: 'date-format',
    SPECIAL_CHARACTERS: 'special-characters',
    NUMBER: 'number',
    MAX_LENGTH: 'max-length',
    FUTURE_DATES: 'future-dates',
    ABOUT_DAYS: 'about-days',
    SPECIAL_CHARACTERS_PARTER_NAME: 'special-characters-parter-name',
    SPECIAL_CHARACTERS_SUPPLIER_NAME: 'special-characters-supplier-name',
    INVALID_PASSWORD_SPECIAL_CHAR: 'invalid-password-special',
    NO_NUMBER: 'no-number',
    DATE_OF_EXISTENCE: 'date-of-existence',
    INVALID_EMAIL: 'invalid-email',
    INVALID_NAME_FILE: 'invalid-name-file',
    RISK_FACTOR_MIN: 'risk-factor-validate-min',
    RISK_FACTOR_MAX: 'risk-factor-validate-max',
    PLAN_DELIVERY_MIN: 'plan-delivery-min',
    PLAN_DELIVERY_MAX: 'plan-delivery-max',
    TOTAL_ON_TIME_DELIVERY_MIN: 'total-on-time-delivery-min',
    TOTAL_ON_TIME_DELIVERY_MAX: 'total-on-time-delivery-max',
    TOTAL_ON_TIME_DELIVERY_MORE: 'total-on-time-delivery-more',
    TASK_MGT_MIN: 'task-mgt-min',
    TASK_MGT_MAX: 'task-mgt-max',
    PHONE_NUMBER: 'invalid-phone-number',
    SKILL_CV_EXIST: 'skill-cv-exist',
    INTERGER: 'number-integer',
    NUMBER_LEAVE_DAYS_USED: 'validate-number-leave-days-used',
    MAX_PHONE_NUMBER: 'phone-number-max',
    FORMAT_PHONE_NUMBER: 'phone-number-format',
    INVALID_PASSWORD_MIN: 'invalid-password-min',
    INVALID_PASSWORD_MAX: 'invalid-password-max',
    REPASSWORD_NOT_MATCHING: 'repassword-not-matching',
    INVALID_CITIZENID_MAX: 'invalid-citizenId-max',
    INVALID_BUSINESSID_MAX: 'invalid-businessId-max',
    INVALID_TAXCODE_MAX: 'invalid-taxCode-max',
    INVALID_CODE_NAME: 'invalid-code-name',
    LEAVE_SEQUENCE: 'leave-sequence',
    LESS_THAN: 'less-than',
    LARGER_THAN: 'larger-than'
};
