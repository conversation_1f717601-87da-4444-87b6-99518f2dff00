/**
 * WorkFinder Spacing System
 * Based on Figma design analysis and 8px grid system
 * Perfect pixel implementation for consistent UI
 */

// =============================================================================
// SPACING SCALE (8px Grid System)
// =============================================================================
export const SPACING = {
    // Micro spacing (0-8px)
    xs: 2, // 2px  - borders, fine adjustments
    sm: 4, // 4px  - small gaps, input borders
    md: 8, // 8px  - standard micro spacing

    // Small spacing (8-16px)
    base: 8, // 8px  - base unit (1x)
    lg: 12, // 12px - medium gaps (1.5x)
    xl: 16, // 16px - component spacing (2x)

    // Medium spacing (16-32px)
    '2xl': 20, // 20px - section spacing (2.5x)
    '3xl': 24, // 24px - card padding (3x)
    '4xl': 32, // 32px - large spacing (4x)

    // Large spacing (32px+)
    '5xl': 40, // 40px - container spacing (5x)
    '6xl': 48, // 48px - section gaps (6x)
    '7xl': 56, // 56px - large sections (7x)
    '8xl': 64 // 64px - page spacing (8x)
} as const;

// =============================================================================
// BORDER RADIUS SYSTEM
// =============================================================================
export const BORDER_RADIUS = {
    none: 0,
    xs: 2, // Minimal radius
    sm: 4, // Small elements, inputs, buttons (secondary)
    md: 8, // Cards, buttons, containers (primary)
    lg: 12, // Medium components
    xl: 16, // Large components
    '2xl': 20, // Special elements, logos
    '3xl': 24, // Extra large components
    full: 9999 // Pills, badges, circular elements
} as const;

// =============================================================================
// CONTAINER WIDTH SYSTEM
// =============================================================================
export const CONTAINER_WIDTHS = {
    full: '100%', // 1920px - Full viewport
    content: '1800px', // Main content container (with 60px padding each side)
    main: '850px', // Primary content area
    sidebar: '390px', // Sidebar width
    form: '330px', // Form elements optimal width
    drawer: '260px', // Navigation drawer
    appDrawer: '320px', // Application drawer
    modal: '600px', // Modal optimal width
    card: '400px' // Standard card width
} as const;

// =============================================================================
// COMPONENT SPACING PRESETS
// =============================================================================
export const COMPONENT_SPACING = {
    // Header & Navigation
    header: {
        height: 88,
        padding: SPACING['3xl'], // 24px
        gap: SPACING.xl // 16px
    },

    // Cards & Containers
    card: {
        padding: SPACING['3xl'], // 24px
        margin: SPACING['2xl'], // 20px
        gap: SPACING.xl, // 16px
        borderRadius: BORDER_RADIUS.md // 8px
    },

    // Layout Sections
    layout: {
        container: SPACING['5xl'], // 40px - Major container spacing
        section: SPACING['4xl'], // 32px - Section spacing
        component: SPACING['2xl'], // 20px - Component spacing
        element: SPACING.xl, // 16px - Element spacing
        gap: SPACING.lg // 12px - Standard gap
    },

    // Responsive Spacing (for different breakpoints)
    responsive: {
        lg: SPACING['2xl'], // 20px - Large screens
        md: SPACING.xl, // 16px - Medium screens
        sm: SPACING.lg, // 12px - Small screens
        xs: SPACING.md // 8px - Extra small screens
    },

    // Form Elements
    form: {
        fieldGap: SPACING.xl, // 16px - Between form fields
        labelGap: SPACING.md, // 8px - Between label and input
        buttonGap: SPACING.lg, // 12px - Between buttons
        sectionGap: SPACING['3xl'] // 24px - Between form sections
    },

    // Lists & Grids
    list: {
        itemGap: SPACING.lg, // 12px - Between list items
        groupGap: SPACING['2xl'] // 20px - Between list groups
    },

    // Buttons & Interactive Elements
    button: {
        padding: `${SPACING.lg}px ${SPACING.xl}px`, // 12px 16px
        gap: SPACING.md, // 8px - Icon + text gap
        borderRadius: BORDER_RADIUS.sm // 4px
    },

    // Icons
    icon: {
        small: 16, // Small icons
        medium: 20, // Medium icons
        large: 24, // Large icons
        xlarge: 32 // Extra large icons
    }
} as const;

// =============================================================================
// BREAKPOINT-SPECIFIC SPACING
// =============================================================================
export const RESPONSIVE_SPACING = {
    container: {
        xs: SPACING.md, // 8px
        sm: SPACING.xl, // 16px
        md: SPACING['2xl'], // 20px
        lg: SPACING['3xl'], // 24px
        xl: SPACING['5xl'] // 40px
    },
    section: {
        xs: SPACING.xl, // 16px
        sm: SPACING['2xl'], // 20px
        md: SPACING['3xl'], // 24px
        lg: SPACING['4xl'], // 32px
        xl: SPACING['5xl'] // 40px
    }
} as const;

// =============================================================================
// TYPE DEFINITIONS
// =============================================================================
export type SpacingKey = keyof typeof SPACING;
export type BorderRadiusKey = keyof typeof BORDER_RADIUS;
export type ContainerWidthKey = keyof typeof CONTAINER_WIDTHS;

// =============================================================================
// UTILITY FUNCTIONS
// =============================================================================
export const getSpacing = (key: SpacingKey): string => `${SPACING[key]}px`;
export const getBorderRadius = (key: BorderRadiusKey): string => (key === 'full' ? `${BORDER_RADIUS[key]}px` : `${BORDER_RADIUS[key]}px`);
export const getContainerWidth = (key: ContainerWidthKey): string => CONTAINER_WIDTHS[key];

// CSS Custom Properties for runtime usage
export const SPACING_CSS_VARS = {
    '--spacing-xs': `${SPACING.xs}px`,
    '--spacing-sm': `${SPACING.sm}px`,
    '--spacing-md': `${SPACING.md}px`,
    '--spacing-base': `${SPACING.base}px`,
    '--spacing-lg': `${SPACING.lg}px`,
    '--spacing-xl': `${SPACING.xl}px`,
    '--spacing-2xl': `${SPACING['2xl']}px`,
    '--spacing-3xl': `${SPACING['3xl']}px`,
    '--spacing-4xl': `${SPACING['4xl']}px`,
    '--spacing-5xl': `${SPACING['5xl']}px`,
    '--spacing-6xl': `${SPACING['6xl']}px`,
    '--spacing-7xl': `${SPACING['7xl']}px`,
    '--spacing-8xl': `${SPACING['8xl']}px`
} as const;

export default {
    SPACING,
    BORDER_RADIUS,
    CONTAINER_WIDTHS,
    COMPONENT_SPACING,
    RESPONSIVE_SPACING,
    getSpacing,
    getBorderRadius,
    getContainerWidth,
    SPACING_CSS_VARS
};
