// ==============================|| MAIN NAVIGATION CONSTANTS ||============================== //

export interface MainNavItemType {
    id: string;
    title: string;
    path: string;
    external?: boolean;
    disabled?: boolean;
}

const mainNavigation: MainNavItemType[] = [
    { id: 'home', title: 'Home', path: '/' },
    { id: 'jobs', title: 'Find Jobs', path: '/jobs' },
    { id: 'employers', title: 'Employers', path: '/employers' },
    { id: 'candidates', title: 'Candidates', path: '/candidates' },
    { id: 'blog', title: 'Blog', path: '/blog' },
    { id: 'pages', title: 'Pages', path: '/pages' }
];

export default mainNavigation;

// Utility functions
export const getNavItemById = (id: string): MainNavItemType | undefined => {
    return mainNavigation.find((item) => item.id === id);
};

export const getNavItemByPath = (path: string): MainNavItemType | undefined => {
    return mainNavigation.find((item) => item.path === path);
};

export const isActiveNavItem = (currentPath: string, navPath: string): boolean => {
    return currentPath === navPath;
};
