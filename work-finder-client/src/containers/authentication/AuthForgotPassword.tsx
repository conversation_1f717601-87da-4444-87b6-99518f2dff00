import { useState } from 'react';
import { useNavigate } from 'react-router-dom';

// yup
// @ts-ignore - TypeScript module resolution issue with @hookform/resolvers
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from 'yup';

// material-ui
import { LoadingButton } from '@mui/lab';
import { Button, Stack, Typography, Box } from '@mui/material';
import { useTheme } from '@mui/material/styles';

// project imports
import { authSelector } from 'store/slice/authSlice';
import { useAppSelector } from 'app/hooks';
import { FormProvider, Input } from 'components/extended/Form';
import { ROUTER } from 'constants/Routers';

// assets
import GoogleIcon from '@mui/icons-material/Google';

// Form validation schema
const forgotPasswordSchema = yup.object({
    email: yup.string().email('Invalid email format').required('Email is required')
});

interface IForgotPasswordConfig {
    email: string;
}

const forgotPasswordConfig: IForgotPasswordConfig = {
    email: ''
};

// ============================|| FORGOT PASSWORD ||============================ //

const AuthForgotPassword = () => {
    const { loading } = useAppSelector(authSelector);
    const theme = useTheme();
    const navigate = useNavigate();

    const handleSubmit = async (values: IForgotPasswordConfig) => {
        console.log('Forgot password form submitted:', values);
        // TODO: Implement forgot password logic
    };

    const handleGoogleLogin = () => {
        console.log('Login with Google');
        // TODO: Implement Google login
    };

    return (
        <FormProvider
            form={{
                defaultValues: forgotPasswordConfig,
                resolver: yupResolver(forgotPasswordSchema)
            }}
            onSubmit={handleSubmit}
        >
            <Box sx={{ width: '100%' }}>
                <Stack spacing={3}>
                    {/* Email Field */}
                    <Stack spacing={1}>
                        <Typography
                            variant="body2"
                            sx={{
                                fontSize: 14,
                                fontWeight: 600,
                                color: theme.palette.text.primary,
                                mb: 0.5
                            }}
                        >
                            Email
                        </Typography>
                        <Input
                            name="email"
                            placeholder="<EMAIL>"
                            textFieldProps={{
                                size: 'medium',
                                autoComplete: 'email',
                                fullWidth: true,
                                sx: {
                                    '& .MuiOutlinedInput-root': {
                                        borderRadius: 1,
                                        fontSize: 14,
                                        '& .MuiInputBase-input::placeholder': {
                                            color: theme.palette.grey[400],
                                            opacity: 1
                                        }
                                    }
                                }
                            }}
                        />
                    </Stack>

                    {/* Reset Password Button */}
                    <LoadingButton
                        fullWidth
                        loading={loading?.forgotPassword}
                        variant="contained"
                        type="submit"
                        size="large"
                        sx={{
                            py: 1.5,
                            borderRadius: 1.5,
                            textTransform: 'none',
                            fontSize: 18,
                            fontWeight: 800,
                            backgroundColor: theme.palette.primary.main,
                            color: 'white',
                            '&:hover': {
                                backgroundColor: theme.palette.primary.dark
                            }
                        }}
                    >
                        Send Reset Link
                    </LoadingButton>
                </Stack>
            </Box>
        </FormProvider>
    );
};

export default AuthForgotPassword;
