import { useState } from 'react';

// project imports
import { Autocomplete } from 'components/extended/Form';
import { IOption } from 'types';
import { jobCategories } from 'pages/homepage/Config';

interface IJobCategoryProps {
    label?: string;
    name?: string;
    disabled?: boolean;
    required?: boolean;
    handleChange?: (data: any) => void;
}

const JobCategory = ({
    label = 'Job Category',
    name = 'category',
    disabled = false,
    required = false,
    handleChange
}: IJobCategoryProps) => {
    const [categories] = useState<IOption[]>([
        { value: '', label: 'All Categories' },
        ...jobCategories.map((category) => ({
            value: category.id.toString(),
            label: category.title
        }))
    ]);

    return (
        <Autocomplete
            name={name}
            label={label}
            options={categories}
            disabled={disabled}
            required={required}
            handleChange={handleChange}
            isDisableClearable={false}
        />
    );
};

export default JobCategory;
