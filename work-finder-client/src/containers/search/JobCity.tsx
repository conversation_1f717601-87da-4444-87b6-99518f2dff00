// material-ui
import { InputAdornment } from '@mui/material';
import { LocationOnOutlined as LocationIcon } from '@mui/icons-material';

// project imports
import { Input } from 'components/extended/Form';

interface IJobCityProps {
    label?: string;
    name?: string;
    placeholder?: string;
    disabled?: boolean;
    required?: boolean;
    onChangeInput?: React.ChangeEventHandler<HTMLInputElement> | undefined;
}

const JobCity = ({
    label = 'City or postcode',
    name = 'location',
    placeholder = 'City or postcode',
    disabled = false,
    required = false,
    onChangeInput
}: IJobCityProps) => {
    return (
        <Input
            name={name}
            label={label}
            placeholder={placeholder}
            disabled={disabled}
            required={required}
            onChangeInput={onChangeInput}
            textFieldProps={{
                InputProps: {
                    startAdornment: (
                        <InputAdornment position="start">
                            <LocationIcon sx={{ color: '#696969', fontSize: 20 }} />
                        </InputAdornment>
                    ),
                    disableUnderline: true
                },
                variant: 'standard',
                sx: {
                    '& .MuiInputBase-root': {
                        fontSize: 15,
                        color: '#696969',
                        height: 48,
                        '&::before': { display: 'none' },
                        '&::after': { display: 'none' }
                    }
                }
            }}
        />
    );
};

export default JobCity;
