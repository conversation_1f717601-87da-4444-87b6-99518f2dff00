import { useState } from 'react';

// project imports
import { Autocomplete } from 'components/extended/Form';
import { IOption } from 'types';

interface IJobTypeProps {
    label?: string;
    name?: string;
    disabled?: boolean;
    required?: boolean;
    handleChange?: (data: any) => void;
}

const JobType = ({ label = 'Job Type', name = 'jobType', disabled = false, required = false, handleChange }: IJobTypeProps) => {
    const [jobTypes] = useState<IOption[]>([
        { value: '', label: 'All Types' },
        { value: 'full_time', label: 'Full Time' },
        { value: 'part_time', label: 'Part Time' },
        { value: 'contract', label: 'Contract' },
        { value: 'temporary', label: 'Temporary' },
        { value: 'intern', label: 'Internship' },
        { value: 'freelance', label: 'Freelance' }
    ]);

    return (
        <Autocomplete
            name={name}
            label={label}
            options={jobTypes}
            disabled={disabled}
            required={required}
            handleChange={handleChange}
            isDisableClearable={false}
        />
    );
};

export default JobType;
