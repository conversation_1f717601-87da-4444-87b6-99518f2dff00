import { useState, useEffect } from 'react';

// project imports
import { Autocomplete } from 'components/extended/Form';
import { IOption } from 'types';

interface IJobLocationProps {
    label?: string;
    name?: string;
    disabled?: boolean;
    required?: boolean;
    handleChange?: (data: any) => void;
}

const JobLocation = ({
    label = 'City or postcode',
    name = 'location',
    disabled = false,
    required = false,
    handleChange
}: IJobLocationProps) => {
    const [locations] = useState<IOption[]>([
        { value: '', label: 'All Locations' },
        { value: 'london', label: 'London, UK' },
        { value: 'manchester', label: 'Manchester, UK' },
        { value: 'birmingham', label: 'Birmingham, UK' },
        { value: 'glasgow', label: 'Glasgow, Scotland' },
        { value: 'dublin', label: 'Dublin, Ireland' }
    ]);

    return (
        <Autocomplete
            name={name}
            label={label}
            options={locations}
            disabled={disabled}
            required={required}
            handleChange={handleChange}
            isDisableClearable={false}
        />
    );
};

export default JobLocation;
