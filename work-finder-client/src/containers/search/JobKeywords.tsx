// material-ui
import { InputAdornment } from '@mui/material';
import { SearchOutlined as SearchIcon } from '@mui/icons-material';

// project imports
import { Input } from 'components/extended/Form';

interface IJobKeywordsProps {
    label?: string;
    name?: string;
    placeholder?: string;
    disabled?: boolean;
    required?: boolean;
    onChangeInput?: React.ChangeEventHandler<HTMLInputElement> | undefined;
}

const JobKeywords = ({
    label = 'Job title, keywords, or company',
    name = 'keywords',
    placeholder = 'Job title, keywords, or company',
    disabled = false,
    required = false,
    onChangeInput
}: IJobKeywordsProps) => {
    return (
        <Input
            name={name}
            label={label}
            placeholder={placeholder}
            disabled={disabled}
            required={required}
            onChangeInput={onChangeInput}
            textFieldProps={{
                InputProps: {
                    startAdornment: (
                        <InputAdornment position="start">
                            <SearchIcon sx={{ color: '#696969', fontSize: 20 }} />
                        </InputAdornment>
                    ),
                    disableUnderline: true
                },
                variant: 'standard',
                sx: {
                    '& .MuiInputBase-root': {
                        fontSize: 15,
                        color: '#696969',
                        height: 48,
                        '&::before': { display: 'none' },
                        '&::after': { display: 'none' }
                    }
                }
            }}
        />
    );
};

export default JobKeywords;
