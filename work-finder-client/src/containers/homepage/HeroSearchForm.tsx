// material-ui
import { Grid, Box, useTheme, Button } from '@mui/material';

// project imports
import { JobCity, JobKeywords, SearchForm } from 'containers/search';
import { IHomepageSearchConfig, homepageSearchConfig, homepageSearchSchema } from 'pages/homepage/Config';

interface IHeroSearchFormProps {
    formReset?: IHomepageSearchConfig;
    handleSearch: (values: IHomepageSearchConfig) => void;
}

const HeroSearchForm = (props: IHeroSearchFormProps) => {
    const { formReset, handleSearch } = props;
    const theme = useTheme();

    return (
        <SearchForm
            defaultValues={homepageSearchConfig}
            formSchema={homepageSearchSchema}
            formReset={formReset}
            handleSubmit={handleSearch}
        >
            <Box
                sx={{
                    backgroundColor: '#FFFFFF',
                    borderRadius: 2,
                    border: '1px solid #ECEDF2',
                    boxShadow: '0px 6px 15px 0px rgba(64, 79, 104, 0.05)',
                    display: 'flex',
                    flexDirection: { xs: 'column', md: 'row' },
                    alignItems: 'center',
                    minHeight: { xs: 'auto', md: 80 },
                    maxWidth: 740,
                    mx: 'auto',
                    overflow: 'hidden',
                    p: { xs: 2, md: 0 }
                }}
            >
                <Grid container alignItems="center" sx={{ height: '100%', py: { xs: 0, md: 1 } }} spacing={{ xs: 2, md: 0 }}>
                    {/* Keywords Input */}
                    <Grid item xs={12} md={5.2} sx={{ px: { xs: 0, md: 1 } }}>
                        <JobKeywords name="keywords" label="" placeholder="Job title, keywords, or company" />
                    </Grid>

                    {/* Vertical Divider */}
                    <Grid item xs={12} md={0.05} sx={{ display: { xs: 'none', md: 'flex' }, justifyContent: 'center' }}>
                        <Box
                            sx={{
                                width: 2,
                                height: 48,
                                backgroundColor: '#ECEDF2'
                            }}
                        />
                    </Grid>

                    {/* Location Input */}
                    <Grid item xs={12} md={4.2} sx={{ px: { xs: 0, md: 1 } }}>
                        <JobCity name="location" label="" placeholder="City or postcode" />
                    </Grid>

                    {/* Search Button */}
                    <Grid item xs={12} md={2.55} sx={{ px: { xs: 0, md: 1 } }}>
                        <Button
                            type="submit"
                            variant="contained"
                            fullWidth
                            sx={{
                                height: { xs: 60, md: 48 },
                                borderRadius: 2,
                                fontSize: 15,
                                fontWeight: 400,
                                textTransform: 'none',
                                backgroundColor: '#1967D2',
                                '&:hover': {
                                    backgroundColor: '#1557B0'
                                },
                                boxShadow: 'none',
                                minWidth: 'auto',
                                whiteSpace: 'nowrap'
                            }}
                        >
                            Find Jobs
                        </Button>
                    </Grid>
                </Grid>
            </Box>
        </SearchForm>
    );
};

export default HeroSearchForm;
