// material-ui
import { Box, Container, Grid, Typography, Button, useTheme } from '@mui/material';
import { Check } from '@mui/icons-material';

const EmployersSection = () => {
    const theme = useTheme();

    const features = [
        'Bring to the table win-win survival',
        'Capitalize on low hanging fruit to identify',
        'But I must explain to you how all this'
    ];

    return (
        <Container maxWidth="lg" sx={{ py: { xs: 8, md: 12 } }}>
            <Grid container spacing={6} alignItems="center">
                {/* Left Content */}
                <Grid item xs={12} md={6}>
                    <Box>
                        <Typography
                            variant="h3"
                            component="h2"
                            sx={{
                                fontWeight: 500,
                                color: theme.palette.primary.dark,
                                mb: 3,
                                lineHeight: 1.3
                            }}
                        >
                            Millions of Jobs.Find the one <br />
                            that suits you.
                        </Typography>

                        <Typography
                            variant="body1"
                            color="text.secondary"
                            sx={{
                                mb: 4,
                                lineHeight: 1.7,
                                fontSize: '1.1rem'
                            }}
                        >
                            Search all the open positions on the web. Get your own personalized salary estimate. Read reviews on over
                            600,000 companies worldwide.
                        </Typography>

                        {/* Feature List */}
                        <Box sx={{ mb: 4 }}>
                            {features.map((feature, index) => (
                                <Box
                                    key={index}
                                    sx={{
                                        display: 'flex',
                                        alignItems: 'center',
                                        mb: 2
                                    }}
                                >
                                    <Check
                                        sx={{
                                            color: theme.palette.primary.main,
                                            mr: 2,
                                            fontSize: 20
                                        }}
                                    />
                                    <Typography variant="body1" color="text.primary" sx={{ lineHeight: 3 }}>
                                        {feature}
                                    </Typography>
                                </Box>
                            ))}
                        </Box>

                        {/* Get Started Button */}
                        <Button
                            variant="contained"
                            size="large"
                            sx={{
                                px: 4,
                                py: 2,
                                fontSize: '1rem',
                                textTransform: 'none',
                                borderRadius: 2
                            }}
                        >
                            Get Started
                        </Button>
                    </Box>
                </Grid>

                {/* Right Image */}
                <Grid item xs={12} md={6}>
                    <Box sx={{ position: 'relative' }}>
                        {/* Main Image */}
                        <Box
                            component="img"
                            src="https://images.unsplash.com/photo-1600880292203-757bb62b4baf?w=600&h=600&fit=crop&auto=format"
                            alt="Team collaboration"
                            sx={{
                                width: '100%',
                                height: 'auto',
                                borderRadius: 2,
                                boxShadow: '0px 40px 30px rgba(25, 25, 46, 0.04)'
                            }}
                        />

                        {/* Stats Overlay */}
                        <Box
                            sx={{
                                position: 'absolute',
                                bottom: { xs: 20, md: 40 },
                                right: { xs: 20, md: 40 },
                                backgroundColor: 'white',
                                borderRadius: 2,
                                p: 3,
                                boxShadow: '0px 40px 30px rgba(25, 25, 46, 0.04)',
                                minWidth: 200,
                                textAlign: 'center'
                            }}
                        >
                            {/* Avatar Stack */}
                            <Box
                                sx={{
                                    display: 'flex',
                                    justifyContent: 'center',
                                    mb: 2,
                                    position: 'relative'
                                }}
                            >
                                {[1, 2, 3, 4].map((index) => (
                                    <Box
                                        key={index}
                                        component="img"
                                        src={`https://images.unsplash.com/photo-${
                                            1500000000000 + index
                                        }?w=50&h=50&fit=crop&crop=face&auto=format`}
                                        alt={`User ${index}`}
                                        sx={{
                                            width: 50,
                                            height: 50,
                                            borderRadius: '50%',
                                            border: '2px solid white',
                                            ml: index > 0 ? -1 : 0,
                                            zIndex: 5 - index
                                        }}
                                    />
                                ))}

                                {/* Plus Icon */}
                                <Box
                                    sx={{
                                        width: 50,
                                        height: 50,
                                        borderRadius: '50%',
                                        backgroundColor: '#D1DAE5',
                                        border: '2px solid white',
                                        ml: -1,
                                        display: 'flex',
                                        alignItems: 'center',
                                        justifyContent: 'center',
                                        color: 'black',
                                        fontWeight: 500,
                                        fontSize: '1.5rem'
                                    }}
                                >
                                    +
                                </Box>
                            </Box>

                            <Typography
                                variant="h6"
                                component="p"
                                sx={{
                                    fontWeight: 500,
                                    color: theme.palette.primary.dark
                                }}
                            >
                                300k+ Employers
                            </Typography>

                            {/* Check Icon */}
                            <Box
                                sx={{
                                    position: 'absolute',
                                    top: -15,
                                    left: -15,
                                    width: 30,
                                    height: 30,
                                    borderRadius: '50%',
                                    backgroundColor: theme.palette.primary.main,
                                    display: 'flex',
                                    alignItems: 'center',
                                    justifyContent: 'center',
                                    boxShadow: '0px 40px 30px rgba(25, 25, 46, 0.04)'
                                }}
                            >
                                <Check sx={{ color: 'white', fontSize: 18 }} />
                            </Box>
                        </Box>
                    </Box>
                </Grid>
            </Grid>
        </Container>
    );
};

export default EmployersSection;
