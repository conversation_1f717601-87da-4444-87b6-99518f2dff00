import React, { useState, useMemo } from 'react';
import { Box, FormControl, Select, MenuItem, InputLabel, SelectChangeEvent, Tabs, Tab, useTheme, useMediaQuery } from '@mui/material';

// project imports
import { IDynamicFilter, IFilterType } from 'types/job';
import { filterTypesData, defaultFilter } from './FilterConfig';

interface IFilterSectionProps {
    onFilterChange?: (filter: IDynamicFilter) => void;
}

const FilterSection: React.FC<IFilterSectionProps> = ({ onFilterChange }) => {
    const theme = useTheme();
    const isMobile = useMediaQuery(theme.breakpoints.down('md'));

    const [selectedFilter, setSelectedFilter] = useState<IDynamicFilter>(defaultFilter);

    // Get current filter type data
    const currentFilterType = useMemo(() => {
        return filterTypesData.find((type) => type.id === selectedFilter.filterType);
    }, [selectedFilter.filterType]);

    // Handle filter type change
    const handleFilterTypeChange = (event: SelectChangeEvent<string>) => {
        const newFilterType = event.target.value;
        const newFilter: IDynamicFilter = {
            filterType: newFilterType,
            filterValue: 'all' // Reset to "Tất cả" when changing type
        };

        setSelectedFilter(newFilter);
        onFilterChange?.(newFilter);
    };

    // Handle filter value change (dropdown)
    const handleFilterValueChange = (event: SelectChangeEvent<string>) => {
        const newFilter: IDynamicFilter = {
            ...selectedFilter,
            filterValue: event.target.value
        };

        setSelectedFilter(newFilter);
        onFilterChange?.(newFilter);
    };

    // Handle tab change (desktop)
    const handleTabChange = (event: React.SyntheticEvent, newValue: string) => {
        const newFilter: IDynamicFilter = {
            ...selectedFilter,
            filterValue: newValue
        };

        setSelectedFilter(newFilter);
        onFilterChange?.(newFilter);
    };

    return (
        <Box
            sx={{
                display: 'flex',
                gap: { xs: 2, sm: 2, md: 3 },
                flexDirection: { xs: 'column', sm: isMobile ? 'column' : 'row' },
                alignItems: { xs: 'stretch', sm: isMobile ? 'stretch' : 'center' },
                justifyContent: { xs: 'stretch', sm: 'flex-start' },
                mb: { xs: 3, md: 4 },
                px: { xs: 1, sm: 2, md: 2 },
                py: { xs: 0, md: 2 },
                width: '100%'
            }}
        >
            {/* Filter Type Dropdown */}
            <FormControl
                size={isMobile ? 'small' : 'medium'}
                sx={{
                    minWidth: { xs: '100%', sm: 200, md: 240 },
                    maxWidth: { sm: 240, md: 280 },
                    flexShrink: 0,
                    '& .MuiOutlinedInput-root': {
                        borderRadius: 3,
                        backgroundColor: 'background.paper',
                        fontSize: { xs: '0.875rem', md: '1rem' },
                        '& fieldset': {
                            borderColor: 'grey.300',
                            borderWidth: '1px'
                        },
                        '&:hover': {
                            '& > fieldset': {
                                borderColor: 'primary.main'
                            }
                        },
                        '&.Mui-focused': {
                            '& > fieldset': {
                                borderColor: 'primary.main',
                                borderWidth: '2px'
                            }
                        }
                    }
                }}
            >
                <InputLabel
                    sx={{
                        fontWeight: 500,
                        color: 'text.secondary'
                    }}
                >
                    Lọc theo
                </InputLabel>
                <Select
                    value={selectedFilter.filterType}
                    onChange={handleFilterTypeChange}
                    label="Lọc theo"
                    sx={{
                        '& .MuiSelect-select': {
                            fontWeight: 500,
                            color: 'text.primary'
                        }
                    }}
                >
                    {filterTypesData.map((filterType) => (
                        <MenuItem
                            key={filterType.id}
                            value={filterType.id}
                            sx={{
                                py: 1.5,
                                fontWeight: 500
                            }}
                        >
                            {filterType.label}
                        </MenuItem>
                    ))}
                </Select>
            </FormControl>

            {/* Filter Values - Responsive: Tabs on Desktop, Dropdown on Mobile */}
            {isMobile ? (
                /* Mobile: Dropdown */
                <FormControl
                    size="small"
                    sx={{
                        minWidth: '100%',
                        '& .MuiOutlinedInput-root': {
                            borderRadius: 3,
                            backgroundColor: 'background.paper',
                            fontSize: '0.875rem',
                            '& fieldset': {
                                borderColor: 'grey.300',
                                borderWidth: '1px'
                            },
                            '&:hover': {
                                '& > fieldset': {
                                    borderColor: 'primary.main'
                                }
                            },
                            '&.Mui-focused': {
                                '& > fieldset': {
                                    borderColor: 'primary.main',
                                    borderWidth: '2px'
                                }
                            }
                        }
                    }}
                >
                    <InputLabel sx={{ fontWeight: 500 }}>{currentFilterType?.label || 'Lọc'}</InputLabel>
                    <Select
                        value={selectedFilter.filterValue}
                        onChange={handleFilterValueChange}
                        label={currentFilterType?.label || 'Lọc'}
                        sx={{
                            '& .MuiSelect-select': {
                                fontWeight: 500
                            }
                        }}
                    >
                        {currentFilterType?.options.map((option) => (
                            <MenuItem key={option.value} value={option.value} sx={{ py: 1.5 }}>
                                {option.label}
                            </MenuItem>
                        ))}
                    </Select>
                </FormControl>
            ) : (
                /* Desktop: Custom Pill-style Tabs */
                <Box
                    sx={{
                        flexGrow: 1,
                        display: 'flex',
                        alignItems: 'center',
                        overflow: 'hidden',
                        position: 'relative'
                    }}
                >
                    <Tabs
                        value={selectedFilter.filterValue}
                        onChange={handleTabChange}
                        variant="scrollable"
                        scrollButtons="auto"
                        sx={{
                            flexGrow: 1,
                            '& .MuiTabs-root': {
                                minHeight: 'auto'
                            },
                            '& .MuiTabs-flexContainer': {
                                gap: 1
                            },
                            '& .MuiTabs-indicator': {
                                display: 'none' // Hide default indicator
                            },
                            '& .MuiTabs-scrollButtons': {
                                width: 32,
                                height: 32,
                                borderRadius: '50%',
                                backgroundColor: 'background.paper',
                                border: '1px solid',
                                borderColor: 'divider',
                                color: 'text.secondary',
                                '&:hover': {
                                    backgroundColor: 'action.hover',
                                    borderColor: 'primary.main'
                                },
                                '&.Mui-disabled': {
                                    opacity: 0.3
                                }
                            },
                            '& .MuiTab-root': {
                                textTransform: 'none',
                                fontWeight: 500,
                                fontSize: '0.875rem',
                                minHeight: 'auto',
                                minWidth: 'auto',
                                padding: '8px 16px',
                                borderRadius: '20px',
                                color: 'text.secondary',
                                backgroundColor: 'background.paper',
                                border: '1px solid',
                                borderColor: 'divider',
                                transition: 'all 0.2s ease',
                                '&:hover': {
                                    backgroundColor: 'action.hover',
                                    borderColor: 'primary.main'
                                },
                                '&.Mui-selected': {
                                    color: 'white',
                                    backgroundColor: 'primary.main',
                                    borderColor: 'primary.main',
                                    fontWeight: 600,
                                    boxShadow: '0 2px 4px rgba(25, 118, 210, 0.2)',
                                    '&:hover': {
                                        backgroundColor: 'primary.dark'
                                    }
                                }
                            }
                        }}
                    >
                        {currentFilterType?.options.map((option) => (
                            <Tab key={option.value} label={option.label} value={option.value} />
                        ))}
                    </Tabs>
                </Box>
            )}
        </Box>
    );
};

export default FilterSection;
