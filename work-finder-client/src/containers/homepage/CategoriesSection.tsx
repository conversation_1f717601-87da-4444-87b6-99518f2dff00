// material-ui
import { Box, Container, Typography, Grid, Card, CardContent, useTheme, useMediaQuery } from '@mui/material';
import { Campaign, Palette, Code, People, Assignment, Support, LocalHospital, DirectionsCar } from '@mui/icons-material';
import { useState } from 'react';

// project imports
import { jobCategories, IJobCategory } from 'pages/homepage/Config';
import {
    MoneyIcon,
    MegaphoneIcon,
    DesignIcon,
    DevelopmentIcon,
    ProjectManagementIcon,
    HumanResourceIcon,
    CustomerServiceIcon,
    HealthCareIcon,
    AutomotiveIcon
} from 'components/icons';

// Icon mapping
const iconMapping: { [key: string]: any } = {
    MoneyIcon,
    MegaphoneIcon,
    DesignIcon,
    DevelopmentIcon,
    HumanResourceIcon,
    ProjectManagementIcon,
    CustomerServiceIcon,
    HealthCareIcon,
    AutomotiveIcon,

    Code
};

const CategoriesSection = () => {
    const theme = useTheme();
    const isMobile = useMediaQuery(theme.breakpoints.down('sm'));

    // Pagination state
    const [currentPage, setCurrentPage] = useState(1);
    const itemsPerPageMobile = 3;

    // Calculate pagination for mobile
    const totalPages = isMobile ? Math.ceil(jobCategories.length / itemsPerPageMobile) : 1;
    const startIndex = isMobile ? (currentPage - 1) * itemsPerPageMobile : 0;
    const endIndex = isMobile ? startIndex + itemsPerPageMobile : jobCategories.length;
    const displayedCategories = isMobile ? jobCategories.slice(startIndex, endIndex) : jobCategories;

    const getIconComponent = (iconName: string) => {
        const IconComponent = iconMapping[iconName] || Code;
        return (
            <IconComponent
                sx={{
                    fontSize: 35,
                    width: 35,
                    height: 35,
                    color: 'inherit',
                    transition: 'all 0.3s ease',
                    display: 'block',
                    margin: 0,
                    padding: 0
                }}
            />
        );
    };

    const handleCategoryClick = (category: IJobCategory) => {
        console.log('Category clicked:', category);
        // TODO: Navigate to jobs page with category filter
    };

    const handleDotClick = (page: number) => {
        setCurrentPage(page);
    };

    return (
        <Box sx={{ py: { xs: 6, md: 10 }, backgroundColor: '#ECEDF2' }}>
            <Container maxWidth="lg">
                {/* Section Header */}
                <Box textAlign="center" mb={6}>
                    <Typography
                        sx={{
                            fontWeight: 500,
                            fontSize: 30,
                            lineHeight: 1.445,
                            color: '#202124',
                            mb: 2
                        }}
                    >
                        Popular Job Categories
                    </Typography>
                    <Typography
                        sx={{
                            fontWeight: 400,
                            fontSize: 15,
                            lineHeight: 1.445,
                            color: '#696969'
                        }}
                    >
                        2020 jobs live - 293 added today.
                    </Typography>
                </Box>

                {/* Categories Grid */}
                <Grid container spacing={3}>
                    {displayedCategories.map((category, index) => {
                        return (
                            <Grid item xs={12} sm={6} md={4} key={category.id}>
                                <Card
                                    sx={{
                                        cursor: 'pointer',
                                        border: '1px solid #ECEDF2',
                                        borderRadius: 2,
                                        boxShadow: '0px 6px 15px 0px rgba(64, 79, 104, 0.05)',
                                        transition: 'all 0.3s ease',
                                        backgroundColor: '#FFFFFF',
                                        '&:hover': {
                                            '& .category-icon': {
                                                color: '#FFFFFF'
                                            },
                                            '& .category-title': {
                                                color: theme.palette.primary.main
                                            },
                                            '& .icon-background': {
                                                backgroundColor: theme.palette.primary.main
                                            }
                                        }
                                    }}
                                    onClick={() => handleCategoryClick(category)}
                                >
                                    <CardContent sx={{ p: 2.5, height: 110, display: 'flex', alignItems: 'center' }}>
                                        {/* Icon Background */}
                                        <Box
                                            className="icon-background"
                                            sx={{
                                                width: 70,
                                                height: 70,
                                                borderRadius: 2,
                                                backgroundColor: '#ECEDF2',
                                                display: 'flex',
                                                alignItems: 'center',
                                                justifyContent: 'center',
                                                mr: 2.5,
                                                transition: 'all 0.3s ease'
                                            }}
                                        >
                                            <Box
                                                className="category-icon"
                                                sx={{
                                                    color: theme.palette.primary.main,
                                                    transition: 'all 0.3s ease',
                                                    display: 'flex',
                                                    alignItems: 'center',
                                                    justifyContent: 'center',
                                                    width: 35,
                                                    height: 35
                                                }}
                                            >
                                                {getIconComponent(category.icon)}
                                            </Box>
                                        </Box>

                                        {/* Category Info */}
                                        <Box>
                                            <Typography
                                                className="category-title"
                                                sx={{
                                                    fontWeight: 500,
                                                    fontSize: 18,
                                                    lineHeight: 1.445,
                                                    color: '#202124',
                                                    mb: 0.5,
                                                    transition: 'all 0.3s ease'
                                                }}
                                            >
                                                {category.title}
                                            </Typography>
                                            <Typography
                                                sx={{
                                                    fontWeight: 400,
                                                    fontSize: 14,
                                                    lineHeight: 1.445,
                                                    color: '#696969'
                                                }}
                                            >
                                                ({category.openPositions} open positions)
                                            </Typography>
                                        </Box>
                                    </CardContent>
                                </Card>
                            </Grid>
                        );
                    })}
                </Grid>

                {/* Custom Dot Pagination for Mobile */}
                {isMobile && totalPages > 1 && (
                    <Box
                        sx={{
                            display: 'flex',
                            justifyContent: 'center',
                            alignItems: 'center',
                            gap: 1,
                            mt: 4
                        }}
                    >
                        {Array.from({ length: totalPages }, (_, index) => {
                            const pageNumber = index + 1;
                            const isActive = pageNumber === currentPage;

                            return (
                                <Box
                                    key={pageNumber}
                                    onClick={() => handleDotClick(pageNumber)}
                                    sx={{
                                        width: isActive ? 24 : 8,
                                        height: 8,
                                        borderRadius: isActive ? 4 : '50%',
                                        backgroundColor: isActive ? theme.palette.primary.main : '#C4C4C4',
                                        cursor: 'pointer',
                                        transition: 'all 0.3s ease',
                                        '&:hover': {
                                            backgroundColor: isActive ? theme.palette.primary.dark : '#A0A0A0'
                                        }
                                    }}
                                />
                            );
                        })}
                    </Box>
                )}
            </Container>
        </Box>
    );
};

export default CategoriesSection;
