// material-ui
import { Box, Container, Grid, Typography, useTheme } from '@mui/material';

const statsData = [
    {
        id: 1,
        number: '4M',
        description: '4 million daily active users'
    },
    {
        id: 2,
        number: '12k',
        description: 'Over 12k open job positions'
    },
    {
        id: 3,
        number: '20M',
        description: 'Over 20 million stories shared'
    }
];

const SiteStatusSection = () => {
    const theme = useTheme();

    return (
        <Container maxWidth="lg" sx={{ py: { xs: 6, md: 8 } }}>
            <Grid container spacing={4} alignItems="center" justifyContent="center">
                {statsData.map((stat, index) => (
                    <Grid item xs={12} sm={4} key={stat.id} sx={{ textAlign: 'center' }}>
                        <Box>
                            {/* Large Number */}
                            <Typography
                                variant="h2"
                                component="div"
                                sx={{
                                    fontWeight: 500,
                                    fontSize: { xs: '3rem', md: '4rem' },
                                    color: theme.palette.primary.dark,
                                    mb: 1,
                                    lineHeight: 1
                                }}
                            >
                                {stat.number}
                            </Typography>

                            {/* Description */}
                            <Typography
                                variant="body1"
                                color="text.secondary"
                                sx={{
                                    fontSize: '1.1rem',
                                    lineHeight: 1.7
                                }}
                            >
                                {stat.description}
                            </Typography>
                        </Box>
                    </Grid>
                ))}
            </Grid>
        </Container>
    );
};

export default SiteStatusSection;
