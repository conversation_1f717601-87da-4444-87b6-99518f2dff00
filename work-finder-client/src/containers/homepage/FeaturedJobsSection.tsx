import React, { useState, useMemo } from 'react';

// material-ui
import { Box, Container, Typography, Grid, useMediaQuery, useTheme } from '@mui/material';

// project imports
import { JobItem } from 'containers/jobs';
import FilterSection from './FilterSection';
import TableFooter from 'components/extended/Table/TableFooter';
import { featuredJobs } from 'pages/homepage/Config';
import { IJobWithInteraction, IDynamicFilter, IJobPaginationData } from 'types/job';

const FeaturedJobsSection = () => {
    const theme = useTheme();
    const isMobile = useMediaQuery(theme.breakpoints.down('md'));

    // Pagination state with responsive size
    const [pagination, setPagination] = useState<IJobPaginationData>({
        total: featuredJobs.length,
        page: 0, // MUI TablePagination uses 0-based indexing
        size: isMobile ? 3 : 9
    });

    const [currentFilter, setCurrentFilter] = useState<IDynamicFilter>({
        filterType: 'salary',
        filterValue: 'all'
    });

    // Get filtered jobs based on current filter
    const filteredJobs = useMemo(() => {
        // TODO: Implement actual filtering logic
        // For now, return all jobs
        return featuredJobs;
    }, [currentFilter]);

    // Update pagination total when filtered jobs change
    React.useEffect(() => {
        setPagination((prev) => ({ ...prev, total: filteredJobs.length }));
    }, [filteredJobs]);

    // Update pagination size when screen size changes
    React.useEffect(() => {
        setPagination((prev) => ({
            ...prev,
            size: isMobile ? 3 : 9,
            page: 0 // Reset to first page when size changes
        }));
    }, [isMobile]);

    // Get paginated jobs for current page
    const paginatedJobs = useMemo(() => {
        const startIndex = pagination.page * pagination.size;
        const endIndex = startIndex + pagination.size;
        return filteredJobs.slice(startIndex, endIndex);
    }, [filteredJobs, pagination.page, pagination.size]);

    const handleJobClick = (job: IJobWithInteraction) => {
        console.log('Job clicked:', job);
        // TODO: Navigate to job detail page
    };

    const handleBookmarkClick = (job: IJobWithInteraction, event: React.MouseEvent) => {
        event.stopPropagation();
        console.log('Bookmark clicked:', job);
        // TODO: Implement bookmark functionality
    };

    const handleFilterChange = (filter: IDynamicFilter) => {
        console.log('Filter changed:', filter);
        setCurrentFilter(filter);
        // Reset to first page when filter changes
        setPagination((prev) => ({ ...prev, page: 0 }));
        // TODO: Implement filtering logic
    };

    const handlePageChange = (event: React.MouseEvent<HTMLButtonElement> | null, newPage: number) => {
        setPagination((prev) => ({ ...prev, page: newPage }));
    };

    const handleRowsPerPageChange = (event: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
        const newSize = parseInt(event.target.value, 10);
        setPagination((prev) => ({
            ...prev,
            size: newSize,
            page: 0 // Reset to first page
        }));
    };

    return (
        <Box sx={{ py: { xs: 6, md: 10 } }}>
            <Container maxWidth="lg">
                {/* Section Header */}
                <Box textAlign="center" mb={6}>
                    <Typography
                        variant="h2"
                        sx={{
                            fontSize: { xs: '2rem', md: '2.5rem' },
                            fontWeight: 600,
                            mb: 1
                        }}
                    >
                        Việc làm tốt nhất
                    </Typography>
                    <Typography variant="h6" color="text.secondary">
                        Know your worth and find the job that qualify your life
                    </Typography>
                </Box>

                {/* Filter Section */}
                <FilterSection onFilterChange={handleFilterChange} />

                {/* Jobs Grid */}
                <Grid container spacing={3}>
                    {paginatedJobs.map((job) => (
                        <Grid item xs={12} sm={6} md={isMobile ? 12 : 6} lg={4} key={job.id}>
                            <JobItem job={job} variant="default" onJobClick={handleJobClick} onBookmarkClick={handleBookmarkClick} />
                        </Grid>
                    ))}
                </Grid>
            </Container>

            {/* Pagination */}
            <Container maxWidth="lg">
                <TableFooter
                    pagination={{
                        total: filteredJobs.length,
                        page: pagination.page,
                        size: pagination.size
                    }}
                    onPageChange={handlePageChange}
                    onRowsPerPageChange={handleRowsPerPageChange}
                />
            </Container>
        </Box>
    );
};

export default FeaturedJobsSection;
