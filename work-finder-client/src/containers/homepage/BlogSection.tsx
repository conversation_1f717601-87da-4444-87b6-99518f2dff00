// material-ui
import { Box, Container, Grid, Typography, Card, CardMedia, CardContent, Button, useTheme } from '@mui/material';
import { ArrowForward } from '@mui/icons-material';

const blogData = [
    {
        id: 1,
        title: 'Attract Sales And Profits',
        content: 'A job ravenously while Far much that one rank beheld after outside....',
        image: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=400&h=250&fit=crop&auto=format',
        date: 'August 31, 2021',
        comments: '12 Comment'
    },
    {
        id: 2,
        title: '5 Tips For Your Job Interviews',
        content: 'A job ravenously while Far much that one rank beheld after outside....',
        image: 'https://images.unsplash.com/photo-1556157382-97eda2d62296?w=400&h=250&fit=crop&auto=format',
        date: 'August 31, 2021',
        comments: '12 Comment'
    },
    {
        id: 3,
        title: 'An Overworked Newspaper Editor',
        content: 'A job ravenously while Far much that one rank beheld after outside....',
        image: 'https://images.unsplash.com/photo-1504384308090-c894fdcc538d?w=400&h=250&fit=crop&auto=format',
        date: 'August 31, 2021',
        comments: '12 Comment'
    }
];

const BlogSection = () => {
    const theme = useTheme();

    return (
        <Box sx={{ py: { xs: 8, md: 12 }, backgroundColor: '#ECEDF2' }}>
            <Container maxWidth="lg">
                {/* Header */}
                <Box textAlign="center" mb={8}>
                    <Typography
                        variant="h3"
                        component="h2"
                        gutterBottom
                        sx={{
                            fontWeight: 500,
                            color: theme.palette.primary.dark,
                            mb: 2
                        }}
                    >
                        Recent News Articles
                    </Typography>
                    <Typography variant="h6" color="text.secondary" sx={{ fontWeight: 400 }}>
                        Fresh job related news content posted each day.
                    </Typography>
                </Box>

                {/* Blog Cards Grid */}
                <Grid container spacing={4}>
                    {blogData.map((blog) => (
                        <Grid item xs={12} md={4} key={blog.id}>
                            <Card
                                elevation={3}
                                sx={{
                                    height: '100%',
                                    display: 'flex',
                                    flexDirection: 'column',
                                    transition: 'transform 0.3s ease, box-shadow 0.3s ease',
                                    '&:hover': {
                                        transform: 'translateY(-8px)',
                                        boxShadow: '0px 20px 40px rgba(64, 79, 104, 0.15)'
                                    }
                                }}
                            >
                                {/* Blog Image */}
                                <CardMedia
                                    component="img"
                                    height="200"
                                    image={blog.image}
                                    alt={blog.title}
                                    sx={{ borderRadius: '8px 8px 0 0' }}
                                />

                                <CardContent sx={{ flexGrow: 1, p: 3 }}>
                                    {/* Date and Comments */}
                                    <Box
                                        sx={{
                                            display: 'flex',
                                            alignItems: 'center',
                                            gap: 2,
                                            mb: 2
                                        }}
                                    >
                                        <Typography variant="body2" color="text.secondary" sx={{ fontSize: '0.875rem' }}>
                                            {blog.date}
                                        </Typography>
                                        <Box
                                            sx={{
                                                width: 4,
                                                height: 4,
                                                borderRadius: '50%',
                                                backgroundColor: 'text.secondary'
                                            }}
                                        />
                                        <Typography variant="body2" color="text.secondary" sx={{ fontSize: '0.875rem' }}>
                                            {blog.comments}
                                        </Typography>
                                    </Box>

                                    {/* Blog Title */}
                                    <Typography
                                        variant="h6"
                                        component="h3"
                                        sx={{
                                            fontWeight: 500,
                                            color: theme.palette.primary.dark,
                                            mb: 2,
                                            lineHeight: 1.4
                                        }}
                                    >
                                        {blog.title}
                                    </Typography>

                                    {/* Blog Content */}
                                    <Typography
                                        variant="body2"
                                        color="text.secondary"
                                        sx={{
                                            mb: 3,
                                            lineHeight: 1.7
                                        }}
                                    >
                                        {blog.content}
                                    </Typography>

                                    {/* Read More Button */}
                                    <Button
                                        variant="text"
                                        color="primary"
                                        endIcon={<ArrowForward sx={{ fontSize: 16 }} />}
                                        sx={{
                                            textTransform: 'none',
                                            fontSize: '0.875rem',
                                            fontWeight: 400,
                                            p: 0,
                                            '&:hover': {
                                                backgroundColor: 'transparent',
                                                '& .MuiSvgIcon-root': {
                                                    transform: 'translateX(4px)'
                                                }
                                            },
                                            '& .MuiSvgIcon-root': {
                                                transition: 'transform 0.3s ease'
                                            }
                                        }}
                                    >
                                        Read More
                                    </Button>
                                </CardContent>
                            </Card>
                        </Grid>
                    ))}
                </Grid>
            </Container>
        </Box>
    );
};

export default BlogSection;
