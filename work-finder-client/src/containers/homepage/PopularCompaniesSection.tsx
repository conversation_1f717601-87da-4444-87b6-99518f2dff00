import { useState, useMemo } from 'react';

// material-ui
import { Box, Container, Typography, Grid, Card, CardContent, Avatar, Button, Tabs, Tab, useMediaQuery, useTheme } from '@mui/material';
import { Add, Business } from '@mui/icons-material';

// Mock data for companies
const companiesData = [
    {
        id: 1,
        name: 'Google',
        field: 'Technology',
        jobCount: 25,
        logo: 'https://logo.clearbit.com/google.com',
        category: 'technology'
    },
    {
        id: 2,
        name: 'Microsoft',
        field: 'Software Development',
        jobCount: 18,
        logo: 'https://logo.clearbit.com/microsoft.com',
        category: 'technology'
    },
    {
        id: 3,
        name: 'Apple',
        field: 'Consumer Electronics',
        jobCount: 12,
        logo: 'https://logo.clearbit.com/apple.com',
        category: 'technology'
    },
    {
        id: 4,
        name: 'Amazon',
        field: 'E-commerce',
        jobCount: 30,
        logo: 'https://logo.clearbit.com/amazon.com',
        category: 'ecommerce'
    },
    {
        id: 5,
        name: 'Meta',
        field: 'Social Media',
        jobCount: 15,
        logo: 'https://logo.clearbit.com/meta.com',
        category: 'technology'
    },
    {
        id: 6,
        name: 'Netflix',
        field: 'Entertainment',
        jobCount: 8,
        logo: 'https://logo.clearbit.com/netflix.com',
        category: 'media'
    },
    {
        id: 7,
        name: 'Tesla',
        field: 'Automotive',
        jobCount: 22,
        logo: 'https://logo.clearbit.com/tesla.com',
        category: 'automotive'
    },
    {
        id: 8,
        name: 'Spotify',
        field: 'Music Streaming',
        jobCount: 10,
        logo: 'https://logo.clearbit.com/spotify.com',
        category: 'media'
    },
    {
        id: 9,
        name: 'Uber',
        field: 'Transportation',
        jobCount: 16,
        logo: 'https://logo.clearbit.com/uber.com',
        category: 'transportation'
    }
];

const categories = [
    { label: 'Tất cả', value: 'all' },
    { label: 'Công nghệ', value: 'technology' },
    { label: 'Thương mại điện tử', value: 'ecommerce' },
    { label: 'Truyền thông', value: 'media' },
    { label: 'Ô tô', value: 'automotive' },
    { label: 'Vận tải', value: 'transportation' }
];

const PopularCompaniesSection = () => {
    const theme = useTheme();
    const isMobile = useMediaQuery(theme.breakpoints.down('md'));

    const [selectedCategory, setSelectedCategory] = useState('all');
    const [hoveredCompany, setHoveredCompany] = useState<number | null>(null);

    // Filter companies based on selected category
    const filteredCompanies = useMemo(() => {
        if (selectedCategory === 'all') {
            return companiesData;
        }
        return companiesData.filter((company) => company.category === selectedCategory);
    }, [selectedCategory]);

    const handleCategoryChange = (_event: React.SyntheticEvent, newValue: string) => {
        setSelectedCategory(newValue);
    };

    const handleFollowClick = (companyId: number, event: React.MouseEvent) => {
        event.stopPropagation();
        console.log('Follow company:', companyId);
        // TODO: Implement follow functionality
    };

    const handleCompanyClick = (companyId: number) => {
        console.log('Company clicked:', companyId);
        // TODO: Navigate to company detail page
    };

    return (
        <Box sx={{ py: { xs: 6, md: 10 } }}>
            <Container maxWidth="lg">
                {/* Section Header */}
                <Box textAlign="center" mb={6}>
                    <Typography
                        variant="h2"
                        sx={{
                            fontSize: { xs: '2rem', md: '2.5rem' },
                            fontWeight: 600,
                            mb: 1
                        }}
                    >
                        Công ty phổ biến
                    </Typography>
                    <Typography variant="h6" color="text.secondary">
                        Khám phá các công ty hàng đầu đang tuyển dụng
                    </Typography>
                </Box>

                {/* Category Tabs */}
                <Box sx={{ mb: 4, display: 'flex', justifyContent: 'center' }}>
                    <Tabs
                        value={selectedCategory}
                        onChange={handleCategoryChange}
                        variant={isMobile ? 'scrollable' : 'standard'}
                        scrollButtons="auto"
                        sx={{
                            '& .MuiTab-root': {
                                textTransform: 'none',
                                fontWeight: 500,
                                fontSize: '1rem',
                                minWidth: 'auto',
                                px: 3
                            }
                        }}
                    >
                        {categories.map((category) => (
                            <Tab key={category.value} label={category.label} value={category.value} />
                        ))}
                    </Tabs>
                </Box>

                {/* Companies Grid */}
                <Grid container spacing={3}>
                    {filteredCompanies.map((company) => (
                        <Grid item xs={12} sm={6} md={4} lg={4} key={company.id}>
                            <Card
                                sx={{
                                    height: '100%',
                                    cursor: 'pointer',
                                    transition: 'all 0.3s ease',
                                    border: '1px solid',
                                    borderColor: 'divider',
                                    position: 'relative',
                                    '&:hover': {
                                        transform: 'translateY(-4px)',
                                        boxShadow: '0px 12px 24px rgba(64, 79, 104, 0.15)',
                                        borderColor: theme.palette.primary.main
                                    }
                                }}
                                onMouseEnter={() => setHoveredCompany(company.id)}
                                onMouseLeave={() => setHoveredCompany(null)}
                                onClick={() => handleCompanyClick(company.id)}
                            >
                                <CardContent sx={{ p: 3, textAlign: 'center' }}>
                                    {/* Company Logo */}
                                    <Avatar
                                        src={company.logo}
                                        sx={{
                                            width: 64,
                                            height: 64,
                                            mx: 'auto',
                                            mb: 2,
                                            backgroundColor: 'grey.100'
                                        }}
                                    >
                                        <Business />
                                    </Avatar>

                                    {/* Company Name */}
                                    <Typography
                                        variant="h6"
                                        component="h3"
                                        sx={{
                                            fontWeight: 600,
                                            mb: 1,
                                            color: theme.palette.primary.dark
                                        }}
                                    >
                                        {company.name}
                                    </Typography>

                                    {/* Company Field */}
                                    <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
                                        {company.field}
                                    </Typography>

                                    {/* Job Count */}
                                    <Typography variant="body2" color="primary" sx={{ fontWeight: 500 }}>
                                        {company.jobCount} việc làm
                                    </Typography>

                                    {/* Follow Button - Shows on hover */}
                                    {hoveredCompany === company.id && (
                                        <Button
                                            variant="contained"
                                            size="small"
                                            startIcon={<Add />}
                                            onClick={(e) => handleFollowClick(company.id, e)}
                                            sx={{
                                                position: 'absolute',
                                                top: 16,
                                                right: 16,
                                                minWidth: 'auto',
                                                px: 2,
                                                py: 1,
                                                fontSize: '0.875rem',
                                                textTransform: 'none'
                                            }}
                                        >
                                            Theo dõi
                                        </Button>
                                    )}
                                </CardContent>
                            </Card>
                        </Grid>
                    ))}
                </Grid>
            </Container>
        </Box>
    );
};

export default PopularCompaniesSection;
