// material-ui
import { Box, Container, Grid, Typography, Button, useTheme } from '@mui/material';
import { Apple, Shop } from '@mui/icons-material';

const AppsSection = () => {
    const theme = useTheme();

    return (
        <Container maxWidth="lg" sx={{ py: { xs: 8, md: 12 } }}>
            <Grid container spacing={6} alignItems="center">
                {/* Left Image */}
                <Grid item xs={12} md={6}>
                    <Box
                        sx={{
                            position: 'relative',
                            textAlign: 'center'
                        }}
                    >
                        {/* Background Gradient */}
                        <Box
                            sx={{
                                position: 'absolute',
                                top: '10%',
                                left: '20%',
                                right: '20%',
                                bottom: 0,
                                background: 'linear-gradient(180deg, rgba(245, 247, 252, 0) 0%, #F5F7FC 100%)',
                                borderRadius: '200px 200px 0 0',
                                zIndex: 0
                            }}
                        />

                        {/* Phone Mockup */}
                        <Box
                            component="img"
                            src="https://images.unsplash.com/photo-1512941937669-90a1b58e7e9c?w=300&h=600&fit=crop&auto=format"
                            alt="Mobile App"
                            sx={{
                                maxWidth: '100%',
                                height: 'auto',
                                maxHeight: 500,
                                position: 'relative',
                                zIndex: 1,
                                filter: 'drop-shadow(0px 20px 40px rgba(25, 25, 46, 0.15))'
                            }}
                        />
                    </Box>
                </Grid>

                {/* Right Content */}
                <Grid item xs={12} md={6}>
                    <Box>
                        {/* Small Title */}
                        <Typography
                            variant="h6"
                            component="h3"
                            sx={{
                                color: theme.palette.primary.main,
                                fontWeight: 500,
                                mb: 2,
                                textTransform: 'uppercase',
                                letterSpacing: '0.1em'
                            }}
                        >
                            Download & Enjoy
                        </Typography>

                        {/* Main Title */}
                        <Typography
                            variant="h3"
                            component="h2"
                            sx={{
                                fontWeight: 500,
                                color: theme.palette.primary.dark,
                                mb: 3,
                                lineHeight: 1.3
                            }}
                        >
                            Get the Jobio Job Search App
                        </Typography>

                        {/* Description */}
                        <Typography
                            variant="body1"
                            color="text.secondary"
                            sx={{
                                mb: 4,
                                lineHeight: 1.7,
                                fontSize: '1.1rem'
                            }}
                        >
                            Search through millions of jobs and find the right fit. Simply swipe right to apply.
                        </Typography>

                        {/* Download Buttons */}
                        <Box sx={{ display: 'flex', gap: 2, flexWrap: 'wrap' }}>
                            {/* Apple App Store Button */}
                            <Button
                                variant="contained"
                                startIcon={<Apple />}
                                sx={{
                                    backgroundColor: theme.palette.primary.dark,
                                    color: 'white',
                                    px: 3,
                                    py: 2,
                                    borderRadius: 2,
                                    textTransform: 'none',
                                    minWidth: 200,
                                    '&:hover': {
                                        backgroundColor: theme.palette.primary.main
                                    }
                                }}
                            >
                                <Box>
                                    <Typography variant="caption" display="block">
                                        Download on the
                                    </Typography>
                                    <Typography variant="h6" sx={{ fontWeight: 500, lineHeight: 1 }}>
                                        Apple Store
                                    </Typography>
                                </Box>
                            </Button>

                            {/* Google Play Store Button */}
                            <Button
                                variant="contained"
                                startIcon={<Shop />}
                                sx={{
                                    backgroundColor: theme.palette.primary.dark,
                                    color: 'white',
                                    px: 3,
                                    py: 2,
                                    borderRadius: 2,
                                    textTransform: 'none',
                                    minWidth: 200,
                                    '&:hover': {
                                        backgroundColor: theme.palette.primary.main
                                    }
                                }}
                            >
                                <Box>
                                    <Typography variant="caption" display="block">
                                        Get in on
                                    </Typography>
                                    <Typography variant="h6" sx={{ fontWeight: 500, lineHeight: 1 }}>
                                        Google Play
                                    </Typography>
                                </Box>
                            </Button>
                        </Box>
                    </Box>
                </Grid>
            </Grid>
        </Container>
    );
};

export default AppsSection;
