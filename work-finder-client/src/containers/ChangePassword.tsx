import React from 'react';
import { Dialog, DialogTitle, DialogContent, DialogActions, Button } from '@mui/material';

interface ChangePasswordModalProps {
    open: boolean;
    handleClose: () => void;
}

const ChangePasswordModal: React.FC<ChangePasswordModalProps> = ({ open, handleClose }) => {
    return (
        <Dialog open={open} onClose={handleClose} maxWidth="sm" fullWidth>
            <DialogTitle>Change Password</DialogTitle>
            <DialogContent>
                {/* Add password change form here */}
                <p>Password change functionality coming soon...</p>
            </DialogContent>
            <DialogActions>
                <Button onClick={handleClose}>Cancel</Button>
                <Button variant="contained" onClick={handleClose}>
                    Save
                </Button>
            </DialogActions>
        </Dialog>
    );
};

export default ChangePasswordModal;
