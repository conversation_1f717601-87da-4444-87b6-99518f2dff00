import React from 'react';
import { Box, Skeleton } from '@mui/material';

// project imports
import MainCard from 'components/cards/MainCard';

interface JobItemSkeletonProps {
    variant?: 'default' | 'compact' | 'featured' | 'list';
    sx?: object;
}

const JobItemSkeleton: React.FC<JobItemSkeletonProps> = ({ variant = 'default', sx = {} }) => {
    const getSkeletonSizes = () => {
        switch (variant) {
            case 'compact':
                return { avatar: 40, padding: 2 };
            case 'list':
                return { avatar: 60, padding: 3 };
            default:
                return { avatar: 60, padding: 3 };
        }
    };

    const sizes = getSkeletonSizes();

    const skeletonContent = (
        <Box display="flex" gap={2}>
            {/* Company Logo Skeleton */}
            <Skeleton variant="circular" width={sizes.avatar} height={sizes.avatar} sx={{ flexShrink: 0 }} />

            {/* Job Info Skeleton */}
            <Box flex={1} minWidth={0}>
                {/* Job Title */}
                <Skeleton variant="text" width="80%" height={variant === 'compact' ? 24 : 28} sx={{ mb: 0.5 }} />

                {/* Company Name */}
                <Skeleton variant="text" width="60%" height={24} sx={{ mb: 1 }} />

                {/* Job Details */}
                <Box display="flex" gap={2} mb={1} flexWrap="wrap">
                    <Skeleton variant="text" width={100} height={20} />
                    <Skeleton variant="text" width={80} height={20} />
                    <Skeleton variant="text" width={90} height={20} />
                </Box>

                {/* Tags */}
                <Box display="flex" gap={1} mb={variant === 'compact' ? 0 : 2}>
                    <Skeleton variant="rounded" width={60} height={24} />
                    <Skeleton variant="rounded" width={50} height={24} />
                    <Skeleton variant="rounded" width={55} height={24} />
                </Box>

                {/* Apply Button Skeleton - only for non-compact variants */}
                {variant !== 'compact' && <Skeleton variant="rounded" width={100} height={32} />}
            </Box>

            {/* Bookmark Button Skeleton */}
            <Skeleton variant="circular" width={40} height={40} sx={{ alignSelf: 'flex-start' }} />
        </Box>
    );

    return (
        <MainCard
            content={true}
            sx={{
                borderRadius: 2,
                ...sx
            }}
            contentSX={{ p: sizes.padding }}
        >
            {skeletonContent}
        </MainCard>
    );
};

export default JobItemSkeleton;
