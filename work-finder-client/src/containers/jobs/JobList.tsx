import React from 'react';
import { <PERSON>, Grid, Button, Typography, Stack } from '@mui/material';
import { ViewListOutlined as ListIcon, ViewModuleOutlined as GridIcon } from '@mui/icons-material';

// project imports
import JobItem from './JobItem';
import JobItemSkeleton from './JobItemSkeleton';
import { IJobListProps } from 'types/job';

const JobList: React.FC<IJobListProps> = ({
    jobs,
    loading = false,
    variant = 'grid',
    itemVariant = 'default',
    showLoadMore = false,
    showFilters = false,
    onLoadMore,
    onJobClick,
    onBookmarkClick,
    onApplyClick,
    onFiltersChange
}) => {
    if (loading && jobs.length === 0) {
        return (
            <Box>
                {variant === 'grid' ? (
                    <Grid container spacing={3}>
                        {Array.from({ length: 6 }).map((_, index) => (
                            <Grid item xs={12} md={6} key={index}>
                                <JobItemSkeleton variant={itemVariant} />
                            </Grid>
                        ))}
                    </Grid>
                ) : (
                    <Stack spacing={2}>
                        {Array.from({ length: 8 }).map((_, index) => (
                            <JobItemSkeleton key={index} variant={itemVariant} />
                        ))}
                    </Stack>
                )}
            </Box>
        );
    }

    if (!loading && jobs.length === 0) {
        return (
            <Box display="flex" flexDirection="column" alignItems="center" justifyContent="center" py={8} textAlign="center">
                <Typography variant="h6" color="text.secondary" mb={1}>
                    No jobs found
                </Typography>
                <Typography variant="body2" color="text.secondary">
                    Try adjusting your search criteria or check back later for new opportunities.
                </Typography>
            </Box>
        );
    }

    const renderJobItems = () => {
        if (variant === 'grid') {
            return (
                <Grid container spacing={3}>
                    {jobs.map((job) => (
                        <Grid item xs={12} md={6} key={job.id}>
                            <JobItem
                                job={job}
                                variant={itemVariant}
                                onJobClick={onJobClick}
                                onBookmarkClick={onBookmarkClick}
                                onApplyClick={onApplyClick}
                                showApplicationButton={itemVariant !== 'compact'}
                            />
                        </Grid>
                    ))}
                </Grid>
            );
        }

        // List variant
        return (
            <Stack spacing={2}>
                {jobs.map((job) => (
                    <JobItem
                        key={job.id}
                        job={job}
                        variant={itemVariant === 'default' ? 'list' : itemVariant}
                        onJobClick={onJobClick}
                        onBookmarkClick={onBookmarkClick}
                        onApplyClick={onApplyClick}
                        showApplicationButton={itemVariant !== 'compact'}
                    />
                ))}
            </Stack>
        );
    };

    return (
        <Box>
            {/* Job Items */}
            {renderJobItems()}

            {/* Loading more items */}
            {loading && jobs.length > 0 && (
                <Box mt={3}>
                    {variant === 'grid' ? (
                        <Grid container spacing={3}>
                            {Array.from({ length: 2 }).map((_, index) => (
                                <Grid item xs={12} md={6} key={`loading-${index}`}>
                                    <JobItemSkeleton variant={itemVariant} />
                                </Grid>
                            ))}
                        </Grid>
                    ) : (
                        <Stack spacing={2}>
                            {Array.from({ length: 3 }).map((_, index) => (
                                <JobItemSkeleton key={`loading-${index}`} variant={itemVariant} />
                            ))}
                        </Stack>
                    )}
                </Box>
            )}

            {/* Load More Button */}
            {showLoadMore && onLoadMore && !loading && (
                <Box textAlign="center" mt={4}>
                    <Button
                        variant="outlined"
                        size="large"
                        onClick={onLoadMore}
                        sx={{
                            px: 4,
                            py: 1.5,
                            borderRadius: 2,
                            fontSize: '1rem',
                            fontWeight: 600
                        }}
                    >
                        Load More Jobs
                    </Button>
                </Box>
            )}
        </Box>
    );
};

export default JobList;
