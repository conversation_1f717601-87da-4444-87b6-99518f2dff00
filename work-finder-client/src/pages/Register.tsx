import { useEffect } from 'react';
import { Location, Navigate, useLocation, Link } from 'react-router-dom';

// material-ui
import { Stack, Typography, Box } from '@mui/material';
import { useTheme } from '@mui/material/styles';

// project imports
import { authSelector } from 'store/slice/authSlice';
import { useAppSelector } from 'app/hooks';
import { AuthRegister } from 'containers/authentication';
import { ESTATUS_LOGIN } from 'constants/Common';
import { DASHBOARD_PATH } from 'constants/Config';

// ================================|| AUTH - REGISTER ||================================ //

const Register = () => {
    const location = useLocation();
    const { status } = useAppSelector(authSelector);
    const theme = useTheme();

    const { state }: Location = location;

    if (status === ESTATUS_LOGIN.SUCCESS) {
        return <Navigate to={state ? `${state.from.pathname}${state.from?.search}` : DASHBOARD_PATH} />;
    }

    return (
        <Box sx={{ width: '100%' }}>
            {/* Register Header */}
            <Stack spacing={1} sx={{ mb: 4 }}>
                <Typography
                    variant="h2"
                    sx={{
                        fontWeight: 700,
                        fontSize: { xs: 28, md: 36 },
                        color: theme.palette.text.primary,
                        textAlign: { xs: 'center', md: 'left' }
                    }}
                >
                    Create Account
                </Typography>
                <Typography
                    variant="body1"
                    sx={{
                        fontSize: 16,
                        color: theme.palette.text.secondary,
                        textAlign: { xs: 'center', md: 'left' }
                    }}
                >
                    Join our community and start your journey today
                </Typography>
            </Stack>

            {/* Register Form */}
            <AuthRegister />

            {/* Register Footer */}
            <Stack direction="row" spacing={1} justifyContent="center" sx={{ mt: 4 }}>
                <Typography variant="body2" color="text.secondary">
                    Already have an account?
                </Typography>
                <Typography
                    variant="body2"
                    component={Link}
                    to="/login"
                    sx={{
                        color: theme.palette.primary.main,
                        fontWeight: 600,
                        textDecoration: 'none',
                        '&:hover': {
                            textDecoration: 'underline'
                        }
                    }}
                >
                    Sign in
                </Typography>
            </Stack>
        </Box>
    );
};

export default Register;
