import * as Yup from 'yup';

export interface IForgotConfig {
    // Add any forgot password configuration fields here
    email?: string;
    resetToken?: string;
}

export const defaultForgotConfig: IForgotConfig = {
    email: '',
    resetToken: ''
};

export interface ILoginConfig {
    username: string;
    password: string;
    rememberMe: boolean;
}

export const loginConfig: ILoginConfig = {
    username: '',
    password: '',
    rememberMe: false
};

export const loginSchema = Yup.object().shape({
    username: Yup.string().required('Username is required').min(3, 'Username must be at least 3 characters'),
    password: Yup.string().required('Password is required').min(6, 'Password must be at least 6 characters'),
    rememberMe: Yup.boolean()
});
