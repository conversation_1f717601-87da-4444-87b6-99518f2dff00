import { useEffect } from 'react';
import { Location, Navigate, useLocation, Link } from 'react-router-dom';

// material-ui
import { Stack, Typography, Box } from '@mui/material';
import { useTheme } from '@mui/material/styles';

// project imports
import { authSelector } from 'store/slice/authSlice';
import { useAppSelector } from 'app/hooks';
import { AuthLogin } from 'containers/authentication';
import { ESTATUS_LOGIN } from 'constants/Common';
import { DASHBOARD_PATH } from 'constants/Config';

// ================================|| AUTH - LOGIN ||================================ //

const Login = () => {
    const location = useLocation();
    const { status } = useAppSelector(authSelector);
    const theme = useTheme();

    const { state }: Location = location;

    if (status === ESTATUS_LOGIN.SUCCESS) {
        return <Navigate to={state ? `${state.from.pathname}${state.from?.search}` : DASHBOARD_PATH} />;
    }

    return (
        <Box sx={{ width: '100%' }}>
            {/* Login Header */}
            <Stack spacing={1} sx={{ mb: 4 }}>
                <Typography
                    variant="h2"
                    sx={{
                        fontWeight: 700,
                        fontSize: { xs: 28, md: 36 },
                        color: theme.palette.text.primary,
                        textAlign: { xs: 'center', md: 'left' }
                    }}
                >
                    Login to your Account
                </Typography>
                <Typography
                    variant="body1"
                    sx={{
                        fontSize: 16,
                        color: theme.palette.text.secondary,
                        textAlign: { xs: 'center', md: 'left' }
                    }}
                >
                    See what is going on with your business
                </Typography>
            </Stack>

            {/* Login Form */}
            <AuthLogin />

            {/* Login Footer */}
            <Stack direction="row" spacing={1} justifyContent="center" sx={{ mt: 4 }}>
                <Typography variant="body2" color="text.secondary">
                    Not Registered Yet?
                </Typography>
                <Typography
                    variant="body2"
                    component={Link}
                    to="/register"
                    sx={{
                        color: theme.palette.primary.main,
                        fontWeight: 600,
                        textDecoration: 'none',
                        '&:hover': {
                            textDecoration: 'underline'
                        }
                    }}
                >
                    Create an account
                </Typography>
            </Stack>
        </Box>
    );
};

export default Login;
