import { Navigate, Outlet, useLocation } from 'react-router-dom';

// project imports
import { checkAllowedPermission, userAuthorization } from 'utils/authorization';
import { openDeniedPermission } from 'store/slice/deniedPermissionSlice';
import { DASHBOARD_PATH } from 'constants/Config';
import { useAppDispatch } from 'app/hooks';
import { ROUTER } from 'constants/Routers';
import useAuth from 'hooks/useAuth';

// ==============================|| AUTH GUARD ||============================== //

/**
 * Authentication guard for routes
 * @param permissionRequired string
 */
const AuthGuard = ({ permissionRequired }: { permissionRequired?: string[] }) => {
    const location = useLocation();

    const dispatch = useAppDispatch();

    const auth = useAuth();
    if (auth) {
        if (permissionRequired) {
            const { isAllowFunctions } = userAuthorization(permissionRequired);
            const isDeniedPermission = !checkAllowedPermission(permissionRequired[0]);

            if (isDeniedPermission) {
                dispatch(openDeniedPermission());
            }

            return auth ? (
                isAllowFunctions ? (
                    <Outlet />
                ) : (
                    <Navigate to={DASHBOARD_PATH} />
                )
            ) : (
                <Navigate to={`/${ROUTER.authentication.login}`} />
            );
        } else {
            return auth ? <Outlet /> : <Navigate to={`/${ROUTER.authentication.login}`} />;
        }
    } else {
        return <Navigate to={`/${ROUTER.authentication.login}`} replace state={{ from: location }} />;
    }
};

export default AuthGuard;
