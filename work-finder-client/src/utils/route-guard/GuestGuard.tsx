import { Navigate, Outlet, matchRoutes, useLocation } from 'react-router-dom';

import { DASHBOARD_PATH } from 'constants/Config';
import { ROUTER } from 'constants/Routers';
import useAuth from 'hooks/useAuth';

// ==============================|| GUEST GUARD ||============================== //

/**
 * Guest guard for routes having no auth required
 * @param {PropTypes.node} children children element/node
 */

const GuestGuard = () => {
    const currentLocation = useLocation();

    const auth = useAuth();

    if (
        auth &&
        !matchRoutes(
            [
                {
                    path: ROUTER.authentication.confirmEmail
                },
                { path: ROUTER.authentication.forgotPassword }
            ],
            currentLocation
        )?.length
    ) {
        return <Navigate to={DASHBOARD_PATH} />;
    }

    return <Outlet />;
};

export default GuestGuard;
