import { Fragment } from 'react';
import { FieldErrors } from 'react-hook-form';
import { saveAs } from 'file-saver';

// project imports
import { IFieldByTabUser, IOption, IProductivity, IProductivityHcInfo, ISkill } from '../types';
import { STATUS } from '../constants/Common';
import { getCookieByKey } from './cookies';
import { reportUrl } from '../index';
import { SEARCH_TIMESTAMP } from 'constants/Config';

/**
 * format price
 * @param price
 * @return format price
 */
export const formatPrice = (price: any) => {
    return new Intl.NumberFormat('en-US').format(+price);
};

/**
 * format price
 * @param price
 * @return custom format price
 */
export const customFormatPrice = (price: any) => {
    const parts = price.toString().split('.');
    parts[0] = parts[0].replace(/\B(?=(\d{3})+(?!\d))/g, ',');
    return parts.join('.');
};

/**
 * transformRequestOptions
 * @param params
 * @returns
 */
export const transformRequestOptions = (params: any) => {
    let options = '';
    Object.entries<any>(params).forEach(([key, value]) => {
        if (typeof value !== 'object' && value) {
            options += `${key}=${encodeURIComponent(value)}&`;
        } else if (Array.isArray(value)) {
            value.forEach((el) => {
                options += `${key}=${encodeURIComponent(el)}&`;
            });
        }
    });
    return options ? options.slice(0, -1) : options;
};

/**
 * export document
 * @param url
 * @param query
 */
export const exportDocument = (url: any, query?: any) => {
    const accessToken = getCookieByKey('accessToken');
    // const urlDownload = `${baseUrl + '/' + url}?${qs.stringify({ ...query, token: accessToken })}`;
    const urlDownload = `${reportUrl + '/' + url}?${transformRequestOptions({ ...query, token: accessToken })}`;
    const win = window.open(urlDownload, '_blank')!;
    win.focus();
};

export const downloadDocument = (fileName: string, data: Blob) => {
    const blob = new Blob([data]);
    saveAs(blob, fileName);
};

/**
 * format tableCell project
 * @param projects
 * @param character
 * @return
 */
export const formatTableCellMemberInProject = (projects: string | null, character?: string) => {
    let splitCharacter = character ? character : ';';
    if (!projects) return <span></span>;
    const formatProjects = projects.split(splitCharacter);

    if (formatProjects.length > 1) {
        return formatProjects.map((item: string, key: number) => (
            <Fragment key={key}>
                <span>{item.trim()}</span>
                {item.trim() && <br />}
            </Fragment>
        ));
    }

    return projects.includes(splitCharacter) ? projects.slice(0, -1) : projects;
};
/**
 * format tableCell project
 * @param {project: string[]}
 * @return {ReactNode} format tablecell
 */
export const formatTableCellProject = (projects: string[]) => {
    return projects.map((project, key) => (
        <Fragment key={key}>
            <span>{project}</span>
            <br />
        </Fragment>
    ));
};

/**
 * convert status
 * @param status
 * @return status user
 */
export const convertStatus = (status: string) => {
    return !!status ? STATUS.filter((el) => el.value === status)[0].label : <></>;
};

/**
 * isEmpty return true when value: undefined, null, "", [], {}
 * @param obj
 * @return {boolean} boolean
 */
export const isEmpty = (value: any): boolean => {
    return (
        // null or undefined
        value == null ||
        // has length and it's zero
        (value.hasOwnProperty('length') && value.length === 0) ||
        // is an Object and has no keys
        (value.constructor === Object && Object.keys(value).length === 0)
    );
};

/**
 * Validate file format
 * @param file
 * @return {boolean} boolean
 */
export const validateFileFormat = (file: File): boolean => {
    const validExtensions = ['xlsx', 'xls'];
    const fileExtension = file.name.split('.')[1];
    return validExtensions.includes(fileExtension);
};

/**
 * isNotNumber
 */
export const isNotNumber = (string: any) => {
    if (string) {
        if (string?.length === 0) {
            // Nếu chuỗi rỗng, coi là chuỗi
            return true;
        } else if (string[0] === '0') {
            // Nếu có số 0 ở đầu chuỗi, coi là chuỗi
            if (string?.length === 1) {
                return false;
            } else {
                return true;
            }
        } else {
            if (!isNaN(string)) {
                // Kiểm tra xem có thể chuyển đổi thành số hay không
                return false;
            } else {
                return true;
            }
        }
    }
};

/**
 * get PARAMS URL
 * @param {string[]} keyParams
 * @param {URLSearchParams} searchParams
 * @return {object} object urlParams
 */
export const getSearchParam = (keyParams: string[], searchParams: URLSearchParams, keyParamsArray?: string[]): object => {
    let urlParams: { [key: string]: any } = {};

    for (const key of keyParams) {
        !isNotNumber(searchParams.get(key)! as any)
            ? (urlParams[key] = searchParams.get(key) ? +searchParams.get(key)! : null)
            : (urlParams[key] = searchParams.get(key));
    }
    if (keyParamsArray) {
        for (const key of keyParamsArray) {
            let isArrayNumber = !isNaN(searchParams.getAll(key)[0] as any);
            urlParams[key] = isArrayNumber ? convertArrayStringToNumber(searchParams.getAll(key)) : searchParams.getAll(key);
        }
    }
    return urlParams;
};

/**
 * Check invalid value in object
 * delete object key have value: null, undefined, '', {}, []
 * @param {object} value
 */
export const transformObject = (obj: any, ignoreKeys?: string[]) => {
    Object.keys(obj).forEach((key) => {
        if (isEmpty(obj[key]) || ignoreKeys?.includes(key)) {
            delete obj[key];
        }
    });

    return obj;
};

/**
 * removeExtraSpace
 * @param str
 * @returns
 */
export const removeExtraSpace = (str: any) => {
    const stringFormat = !Array.isArray(str) ? str?.toString()?.trim().replace(/\t/g, '').split(/ +/).join(' ') : str;

    return stringFormat;
};

/**
 * check error tab
 * @param errors
 * @param fields
 * @returns
 */
export const getTabValueByFieldError = (errors: FieldErrors<any>, fields: IFieldByTabUser[]) => {
    let tabValue = 0;
    for (const error of fields) {
        if (error.fields.some((field: string) => errors[field])) {
            tabValue = error.tabValue;
            break;
        }
    }
    return tabValue;
};

/**
 * convert array string to array number
 * @param arr
 * @returns
 */
export function convertArrayStringToNumber(arr: string[]) {
    return arr?.map((str) => {
        return Number(str);
    });
}

/**
 * calculation sum
 * @param numbers
 * @returns
 */
export function calculationSum(...numbers: number[]): number {
    let sum = 0;
    for (let i = 0; i < numbers.length; i++) {
        sum += numbers[i];
    }
    return sum;
}

// Calculate Monthly production performance

export const getDatesFromValue = (value: number, months: IOption[]) => {
    const selectedMonth = months.find((month: IOption) => month.value === value);
    if (selectedMonth) {
        const fromDate = selectedMonth.label.substring(selectedMonth.label.indexOf('(') + 1, selectedMonth.label.indexOf('~')).trim();
        const toDate = selectedMonth.label.substring(selectedMonth.label.indexOf('~') + 1, selectedMonth.label.indexOf(')')).trim();
        return { fromDate, toDate };
    }
    return { fromDate: '', toDate: '' };
};

// Calculate receivable (TM)
export const calculateReceivable = (month: number, paymentTerm: number) => {
    const receivable = month - paymentTerm;
    return receivable;
};

// Calculate amount (TM)
export const calculateAmount = (arr: IProductivityHcInfo[], standardWorkingDay: number) => {
    for (let i = 0; i < arr.length; i++) {
        const { rateUSD, quantity } = arr[i];
        if (quantity && rateUSD !== null) {
            arr[i].amount = +rateUSD * +quantity * standardWorkingDay;
        }
    }
};

// Total amount (TM)
export const calculateTotalAmount = (arr: IProductivityHcInfo[]) => {
    let totalAmount = 0;
    for (let i = 0; i < arr?.length; i++) {
        if (arr[i].amount !== null) {
            totalAmount += parseInt(arr[i].amount);
        }
    }
    return totalAmount;
};

// Calculate Contract size (TM)
export const calculateContractSize = (original: number, exchange: number) => {
    const contractSize = original / exchange;
    return contractSize;
};

// Calculate rate USD (TM)
export const calculateRateUsd = (exchange: number, rate: number) => {
    // extra / usdmd * 23
    const rateUsd = rate / exchange;
    return rateUsd;
};

// Calculate delivered (Fix cost)
export const calculateDeliveredFixCost = (contractSize: number, contractAllocationByMonth: number) => {
    const delivered = contractSize / contractAllocationByMonth;
    return delivered;
};

//Total Delivered
export const calculateTotalDelivered = (data: IProductivity[]) => {
    let totalDelivered = 0;
    if (data) {
        for (let i = 0; i < data.length; i++) {
            totalDelivered += data[i]!.delivered!.value!;
        }
        return totalDelivered;
    }
};
// Delivered month 4 last year
export const deliveredMonthLastYear = (data: IProductivity[], month: number) => {
    if (data) {
        for (const item of data) {
            if (item.month === month) {
                return item!.delivered!.value;
            }
        }
    } else {
        return 0;
    }
};

export const getWorkingDaysByMonth = (months: any, monthNumber: any) => {
    const month = months?.find((m: any) => m.month === monthNumber);
    if (month) {
        return month.workingDays;
    }
    return 0;
};

// Backgroud color
export const getBackgroundColor = (item: string) => {
    return item === 'sat' || item === 'sun' ? '#B1B1B1' : '';
};

/**
 * Get base64
 * @param text
 * @returns
 */
export const getBase64fromReaderResult = (text: string) => {
    return text.replace('data:', '').replace(/^.+,/, '');
};

/**
 * Ponvert file to base64
 * @param file
 * @returns
 */
export const convertFileToBase64 = (file: File): Promise<string> => {
    return new Promise<string>((resolve, reject) => {
        const reader = new FileReader();
        reader.readAsDataURL(file);
        reader.onload = () => resolve(reader.result?.toString() || '');
        reader.onerror = (error) => reject(error);
    });
};

/**
 * show pdf in new tab
 * @param base64Data
 */
export function showPdfInNewTab(base64Data: string) {
    const nav = window.navigator as any;
    if (nav && nav.msSaveOrOpenBlob) {
        var byteCharacters = atob(base64Data);
        var byteNumbers = new Array(byteCharacters.length);
        for (var i = 0; i < byteCharacters.length; i++) {
            byteNumbers[i] = byteCharacters.charCodeAt(i);
        }
        var byteArray = new Uint8Array(byteNumbers);
        var blob = new Blob([byteArray], {
            type: 'application/pdf'
        });
        nav.msSaveOrOpenBlob(blob, 'myreport.pdf');
    } else {
        var pdfWindow: any = window.open('', '_blank');
        const iframeTag = "<iframe width='100%' style='margin: -8px;border: none;' height='100%' src='data:application/pdf;base64, ";
        const iframeTagClose = "'></iframe>";
        pdfWindow.document.write(iframeTag + encodeURI(base64Data) + iframeTagClose);
    }
}
// get data productivity by month
export const getDataProductivityByMonth = (array: IProductivity[], month: number) => {
    return (
        array &&
        array.length > 0 &&
        array.filter((el: IProductivity) => {
            return el.month === month;
        })
    );
};

// get receveiable by month
export const getReceivableByMonth = (array: IProductivity[], month: number) => {
    const matchingData = array.find((el: IProductivity) => el.month === month);
    return matchingData ? matchingData.delivered?.value : 0;
};

// CV config check IsEdit skill
//edit
export const checkAndAddIsEdit = (skillList1: ISkill[], skillList2: ISkill[]) => {
    const newdata = [];

    const skillList2Set = new Set(skillList2.map((item: ISkill) => item.idHexString));

    for (const item of skillList1) {
        const isDelete = !skillList2Set.has(item.idHexString);
        const matchingItem = skillList2.find((item2: ISkill) => item2.idHexString === item.idHexString);
        const name = matchingItem ? matchingItem.name : item.name;
        const new_item = { idHexString: item.idHexString, name, isDelete };
        newdata.push(new_item);
    }

    for (const item of skillList2) {
        if (!item.idHexString) {
            newdata.push({ ...item, isDelete: false });
        } else if (!skillList1.some((item1: any) => item1.idHexString === item.idHexString)) {
            newdata.push({ ...item, isDelete: true });
        }
    }

    return newdata;
};

//add
export const convertValueToNewValue = (value: any) => {
    const newValue = [];

    for (const item of value) {
        if (item.idHexString !== undefined) {
            newValue.push({ name: item.name, isDelete: false });
        } else if (item.name && item.isDelete !== undefined) {
            newValue.push({ name: item.name, isDelete: item.isDelete });
        }
    }

    return newValue;
};

// compare effort member
export const compareStyleEffortMember = (effortVerified?: number | null, effort?: number | null) => {
    return { color: (effortVerified || 0) !== effort ? '#E70A17' : 'inherit' };
};
// local storage
export const setLocalStorageSearchTime = (value: any) => {
    localStorage.setItem(SEARCH_TIMESTAMP, `?${transformRequestOptions(value)}`);
};

export const converStringToCalculationInputsObject = (
    input: string
): {
    sign?: string;
    code?: string;
}[] => {
    return input.split(/([+\-*/])/).reduce<
        {
            sign?: string;
            code?: string;
        }[]
    >((acc, current, index, array) => {
        if (index === 0) {
            acc.push({ sign: '', code: current });
        } else if (index % 2 !== 0) {
            acc.push({ sign: current, code: array[index + 1] });
        }
        return acc;
    }, []);
};

export const convertCalculationInputsToString = (
    calculationInputs: {
        sign?: string;
        code?: IOption;
    }[]
): string => {
    return calculationInputs
        .map((item, index) => {
            if (index === 0) {
                return item.code?.value;
            }
            return `${item.sign}${item.code?.value}`;
        })
        .join('');
};

// Check is holiday
export const checkIsHoliday = (date: Date, holidays: string[]) => {
    const dateStr = `${date.getDate().toString().padStart(2, '0')}/${(date.getMonth() + 1)
        .toString()
        .padStart(2, '0')}/${date.getFullYear()}`;
    return holidays.includes(dateStr);
};
