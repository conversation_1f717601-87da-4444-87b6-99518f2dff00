import { useEffect } from 'react';
import { CircularProgress } from '@mui/material';
import { Box } from '@mui/system';

import { authSelector, getUserInfo } from 'store/slice/authSlice';
import { useAppDispatch, useAppSelector } from 'app/hooks';
import NavigationScroll from 'layout/NavigationScroll';
import Snackbar from 'components/extended/Snackbar';
import Confirm from 'components/extended/Confirm';
import { getCookieByKey } from 'utils/cookies';
import Locales from 'components/Locales';
import ThemeCustomization from 'themes';
import Routes from './Routes';

// ==============================|| APP ||============================== //

const App = () => {
    const { loading, showLoadingScreen } = useAppSelector(authSelector);

    const dispatch = useAppDispatch();

    useEffect(() => {
        const accessToken = getCookieByKey('accessToken');

        if (accessToken) {
            dispatch(getUserInfo({}));
        }
    }, [dispatch]);

    return loading[getUserInfo.typePrefix] && showLoadingScreen ? (
        <Box
            sx={{
                display: 'flex',
                justifyContent: 'center',
                alignItems: 'center',
                width: '100vw',
                height: '100vh'
            }}
        >
            <CircularProgress />
        </Box>
    ) : (
        <ThemeCustomization>
            <Locales>
                <NavigationScroll>
                    <>
                        <Routes />
                        <Snackbar />
                        <Confirm />
                    </>
                </NavigationScroll>
            </Locales>
        </ThemeCustomization>
    );
};

export default App; 