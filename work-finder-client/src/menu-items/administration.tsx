// third-party
import { FormattedMessage } from 'react-intl';

// project imports
import {
    ManageUser,
    ManageProject,
    ManageHoliday,
    ManageSpecialHours,
    ManageGroup,
    ManageRank,
    SystemConfig,
    EmailConfig,
    Compass,
    NonBillableMonitoring
} from 'components/icons';
import { MAIN_FUNCTIONS } from 'constants/Permission';
import { ROUTER } from 'constants/Routers';
import { NavItemType } from 'types';
import { TEXT_CONFIG_SCREEN } from 'constants/Common';

// constant
const icons = {
    ManageUser,
    ManageProject,
    ManageHoliday,
    ManageSpecialHours,
    ManageGroup,
    ManageRank,
    SystemConfig,
    EmailConfig,
    Compass,
    NonBillableMonitoring
};

const { sidebar } = TEXT_CONFIG_SCREEN;
// ==============================|| EXTRA administration MENU ITEMS ||============================== //

const administration: NavItemType = {
    id: 'administration',
    title: <FormattedMessage id={sidebar + 'administration'} />,
    type: 'group',
    access: MAIN_FUNCTIONS.admin.root,
    children: [
        {
            id: 'manage-user',
            title: <FormattedMessage id={sidebar + 'manage-user'} />,
            type: 'item',
            url: `/${ROUTER.administration.manage_user}`,
            icon: icons.ManageUser,
            access: MAIN_FUNCTIONS.admin.user,
            breadcrumbs: true
        },
        {
            id: 'manage-project',
            title: <FormattedMessage id={sidebar + 'manage-project'} />,
            type: 'item',
            url: `/${ROUTER.administration.manage_project}`,
            icon: icons.ManageProject,
            access: MAIN_FUNCTIONS.admin.project,
            breadcrumbs: true
        },
        {
            id: 'manage-holiday',
            title: <FormattedMessage id={sidebar + 'manage-holiday'} />,
            type: 'item',
            url: `/${ROUTER.administration.manage_holiday}`,
            icon: icons.ManageHoliday,
            access: MAIN_FUNCTIONS.admin.holidays,
            breadcrumbs: true
        },
        {
            id: 'manage-special-hours',
            title: <FormattedMessage id={sidebar + 'manage-special-hours'} />,
            type: 'item',
            url: `/${ROUTER.administration.manage_special_hours}`,
            icon: icons.ManageSpecialHours,
            access: MAIN_FUNCTIONS.admin.specialHours,
            breadcrumbs: true
        },
        {
            id: 'manage-group',
            title: <FormattedMessage id={sidebar + 'manage-group'} />,
            type: 'item',
            url: `/${ROUTER.administration.manage_group}`,
            icon: icons.ManageGroup,
            breadcrumbs: true,
            access: MAIN_FUNCTIONS.admin.group
        },
        {
            id: 'manage-rank',
            title: <FormattedMessage id={sidebar + 'manage-rank'} />,
            type: 'item',
            url: `/${ROUTER.administration.manage_rank}`,
            icon: icons.ManageRank,
            breadcrumbs: true,
            access: MAIN_FUNCTIONS.admin.rank
        },
        {
            id: 'system-config',
            title: <FormattedMessage id={sidebar + 'system-config'} />,
            type: 'item',
            icon: icons.SystemConfig,
            url: `/${ROUTER.administration.system_config}`,
            breadcrumbs: true,
            access: MAIN_FUNCTIONS.admin.systemConfig
        },
        {
            id: 'email-config',
            title: <FormattedMessage id={sidebar + 'email-config'} />,
            type: 'item',
            icon: icons.EmailConfig,
            url: `/${ROUTER.administration.email_config}`,
            breadcrumbs: true,
            access: MAIN_FUNCTIONS.admin.emailConfig
        },
        {
            id: 'cv-config',
            title: <FormattedMessage id={sidebar + 'cv-config'} />,
            type: 'item',
            icon: icons.SystemConfig,
            url: `/${ROUTER.administration.cv_config}`,
            breadcrumbs: true,
            access: MAIN_FUNCTIONS.admin.cvConfig
        },
        {
            id: 'exchange-rate-config',
            title: <FormattedMessage id={sidebar + 'exchange-rate-config'} />,
            type: 'item',
            icon: icons.SystemConfig,
            url: `/${ROUTER.administration.exchange_rate_config}`,
            breadcrumbs: true,
            access: MAIN_FUNCTIONS.admin.exChangeRateConfig
        },
        {
            id: 'manage-department',
            title: <FormattedMessage id={sidebar + 'manage-department'} />,
            type: 'item',
            icon: icons.SystemConfig,
            url: `/${ROUTER.administration.manage_department}`,
            breadcrumbs: true,
            access: MAIN_FUNCTIONS.admin.department
        },
        {
            id: 'project-type-config',
            title: <FormattedMessage id={sidebar + 'project-type-config'} />,
            type: 'item',
            icon: icons.Compass,
            url: `/${ROUTER.administration.project_type_config}`,
            breadcrumbs: true,
            access: MAIN_FUNCTIONS.admin.projectTypeConfig
        },
        {
            id: 'title-config',
            title: <FormattedMessage id={sidebar + 'title-config'} />,
            type: 'item',
            icon: icons.SystemConfig,
            url: `/${ROUTER.administration.title_config}`,
            breadcrumbs: true,
            access: MAIN_FUNCTIONS.admin.titleConfig
        },

        {
            id: 'non-billables-config',
            title: <FormattedMessage id={sidebar + 'non-billables-config'} />,
            type: 'item',
            icon: icons.SystemConfig,
            url: `/${ROUTER.administration.non_billables_config}`,
            breadcrumbs: true,
            access: MAIN_FUNCTIONS.admin.nonBillablesConfig
        },
        {
            id: 'flexible-reporting',
            title: <FormattedMessage id={sidebar + 'flexible-reporting'} />,
            type: 'collapse',
            url: `/${ROUTER.administration.flexible_reporting}`,
            breadcrumbs: true,
            access: MAIN_FUNCTIONS.admin.flexibleReporting.root,
            icon: icons.NonBillableMonitoring,
            children: [
                {
                    id: 'column-config',
                    title: <FormattedMessage id={sidebar + 'column-config'} />,
                    type: 'item',
                    url: `/${ROUTER.administration.flexible_reporting.index}/${ROUTER.administration.flexible_reporting.column_config}`,
                    breadcrumbs: true,
                    access: MAIN_FUNCTIONS.admin.flexibleReporting.columnConfig
                },
                {
                    id: 'flexible-reporting-config',
                    title: <FormattedMessage id={sidebar + 'flexible-reporting-config'} />,
                    type: 'item',
                    url: `/${ROUTER.administration.flexible_reporting.index}/${ROUTER.administration.flexible_reporting.flexible_reporting_config}`,
                    breadcrumbs: true,
                    access: MAIN_FUNCTIONS.admin.flexibleReporting.flexibleReportingConfig
                },
                {
                    id: 'text-config',
                    title: <FormattedMessage id={sidebar + 'language-config'} />,
                    type: 'item',
                    url: `/${ROUTER.administration.flexible_reporting.index}/${ROUTER.administration.flexible_reporting.text_config}`,
                    breadcrumbs: true,
                    access: MAIN_FUNCTIONS.admin.flexibleReporting.textConfig
                }
            ]
        }
    ]
};

export default administration;
