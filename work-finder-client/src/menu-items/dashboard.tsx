// third-party
import { FormattedMessage } from 'react-intl';

// project import
import { DashBoard } from 'components/icons';
import { ROUTER } from 'constants/Routers';
import { NavItemType } from 'types';
import { TEXT_CONFIG_SCREEN } from 'constants/Common';

// constant
const icons = {
    DashBoard
};
const { sidebar } = TEXT_CONFIG_SCREEN;
const dashboard: NavItemType = {
    id: '',
    title: <FormattedMessage id={sidebar + 'dashboard'} />,
    type: 'group',
    children: [
        {
            id: '',
            title: <FormattedMessage id={sidebar + 'dashboard'} />,
            type: 'item',
            url: ROUTER.home.index,
            icon: icons.DashBoard,
            breadcrumbs: false
        }
    ]
};

export default dashboard;
