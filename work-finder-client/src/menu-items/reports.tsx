// third-party
import { FormattedMessage } from 'react-intl';

// project imports
import {
    WeeklyEffort,
    MonthlyEffort,
    ListProjectTeam,
    NonBillableMonitoring,
    MonthlyCostMonitoring,
    MonthlyProjectCost,
    MonthlyProductionPerformance,
    SaleList,
    ManageSkills
} from 'components/icons';
import { MAIN_FUNCTIONS } from 'constants/Permission';
import { ROUTER } from 'constants/Routers';
import { NavItemType } from 'types';
import { TEXT_CONFIG_SCREEN } from 'constants/Common';

// constant
const icons = {
    WeeklyEffort,
    MonthlyEffort,
    ListProjectTeam,
    NonBillableMonitoring,
    MonthlyCostMonitoring,
    MonthlyProjectCost,
    MonthlyProductionPerformance,
    SaleList,
    ManageSkills
};

const { sidebar } = TEXT_CONFIG_SCREEN;

// ==============================|| EXTRA REPORTS MENU ITEMS ||============================== //

const reports: NavItemType = {
    id: 'reports',
    title: <FormattedMessage id={sidebar + 'reports'} />,
    type: 'group',
    access: MAIN_FUNCTIONS.reports.root,
    children: [
        // ============== General Report ==============
        {
            id: 'general-report',
            title: <FormattedMessage id={sidebar + 'general-report'} />,
            type: 'collapse',
            icon: icons.MonthlyEffort,
            access: MAIN_FUNCTIONS.reports.generalReport.root,
            children: [
                {
                    id: 'orm-report',
                    title: <FormattedMessage id={sidebar + 'orm-report'} />,
                    type: 'item',
                    url: `/${ROUTER.reports.general_report.index}/${ROUTER.reports.general_report.orm_report}`,
                    breadcrumbs: true,
                    access: MAIN_FUNCTIONS.reports.generalReport.reportORM
                },
                {
                    id: 'product-report',
                    title: <FormattedMessage id={sidebar + 'product-report'} />,
                    type: 'item',
                    url: `/${ROUTER.reports.general_report.index}/${ROUTER.reports.general_report.product}`,
                    breadcrumbs: true,
                    access: MAIN_FUNCTIONS.reports.generalReport.productReport,
                    defaultUrl: true
                },
                {
                    id: 'project-report',
                    title: <FormattedMessage id={sidebar + 'project-report'} />,
                    type: 'item',
                    url: `/${ROUTER.reports.general_report.index}/${ROUTER.reports.general_report.project_report}`,
                    breadcrumbs: true,
                    access: MAIN_FUNCTIONS.reports.generalReport.project_report
                }
            ]
        },
        // ============== Weekly Effort ==============
        {
            id: 'weekly-effort',
            title: <FormattedMessage id={sidebar + 'weekly-effort'} />,
            type: 'item',
            url: `/${ROUTER.reports.weekly_effort}`,
            icon: icons.WeeklyEffort,
            breadcrumbs: true,
            access: MAIN_FUNCTIONS.reports.weeklyEffort.root
        },
        // ============== Monthly Effort ==============
        {
            id: 'monthly-effort',
            title: <FormattedMessage id={sidebar + 'monthly-effort'} />,
            type: 'collapse',
            icon: icons.MonthlyEffort,
            access: MAIN_FUNCTIONS.reports.monthlyEffort.root,
            children: [
                {
                    id: 'summary',
                    title: <FormattedMessage id={sidebar + 'monthly-effort-summary'} />,
                    type: 'item',
                    url: `/${ROUTER.reports.monthly_effort.index}/${ROUTER.reports.monthly_effort.summary}`,
                    breadcrumbs: true,
                    access: MAIN_FUNCTIONS.reports.monthlyEffort.summary
                },
                {
                    id: 'project',
                    title: <FormattedMessage id={sidebar + 'monthly-effort-project'} />,
                    type: 'item',
                    url: `/${ROUTER.reports.monthly_effort.index}/${ROUTER.reports.monthly_effort.project}`,
                    breadcrumbs: true,
                    access: MAIN_FUNCTIONS.reports.monthlyEffort.project
                },
                {
                    id: 'department-member',
                    title: <FormattedMessage id={sidebar + 'monthly-effort-department-member'} />,
                    type: 'item',
                    url: `/${ROUTER.reports.monthly_effort.index}/${ROUTER.reports.monthly_effort.department_member}`,
                    breadcrumbs: true,
                    access: MAIN_FUNCTIONS.reports.monthlyEffort.member
                }
            ]
        },
        // ============== Non-billalbe Monitoring ==============
        {
            id: 'non-billable-monitoring',
            title: <FormattedMessage id={sidebar + 'non-billable-monitoring'} />,
            type: 'collapse',
            url: `/${ROUTER.reports.non_billable_monitoring}`,
            icon: icons.NonBillableMonitoring,
            breadcrumbs: true,
            access: MAIN_FUNCTIONS.reports.nonBillable.root,
            children: [
                {
                    id: 'non-billable-by-member',
                    title: <FormattedMessage id={sidebar + 'non-billable-by-member'} />,
                    type: 'item',
                    url: `/${ROUTER.reports.non_billable_monitoring.index}/${ROUTER.reports.non_billable_monitoring.non_billable_by_member}`,
                    breadcrumbs: true,
                    access: MAIN_FUNCTIONS.reports.nonBillable.nonBillMember
                },
                {
                    id: 'nonbill-ratio-chart',
                    title: <FormattedMessage id={sidebar + 'nonbill-ratio-chart'} />,
                    type: 'item',
                    url: `/${ROUTER.reports.non_billable_monitoring.index}/${ROUTER.reports.non_billable_monitoring.non_billable_cost_by_week}`,
                    breadcrumbs: true,
                    access: MAIN_FUNCTIONS.reports.nonBillable.nonBillChart
                }
            ]
        },
        // ============== List Project Team ==============
        {
            id: 'resources-in-project',
            title: <FormattedMessage id={sidebar + 'resources-in-project'} />,
            type: 'item',
            url: `/${ROUTER.reports.resources_in_project}`,
            icon: icons.ListProjectTeam,
            breadcrumbs: true,
            access: MAIN_FUNCTIONS.reports.resourcesInProjects
        },
        {
            id: 'cost-monitoring',
            title: <FormattedMessage id={sidebar + 'cost-effort-monitoring'} />,
            type: 'collapse',
            url: `/${ROUTER.reports.cost_monitoring}`,
            icon: icons.MonthlyCostMonitoring,
            breadcrumbs: true,
            access: MAIN_FUNCTIONS.reports.costMonitoring.root,
            children: [
                {
                    id: 'weekly-monitoring',
                    title: <FormattedMessage id={sidebar + 'weekly-monitoring'} />,
                    type: 'item',
                    url: `/${ROUTER.reports.cost_monitoring.index}/${ROUTER.reports.cost_monitoring.weekly_cost}`,
                    breadcrumbs: true,
                    access: MAIN_FUNCTIONS.reports.costMonitoring.weeklyCost
                },
                {
                    id: 'monthly-monitoring',
                    title: <FormattedMessage id={sidebar + 'monthly-monitoring'} />,
                    type: 'item',
                    url: `/${ROUTER.reports.cost_monitoring.index}/${ROUTER.reports.cost_monitoring.monthly_cost}`,
                    breadcrumbs: true,
                    access: MAIN_FUNCTIONS.reports.costMonitoring.monthlyCost
                }
            ]
        },
        // ============== Monthly Project Cost ==============
        {
            id: 'monthly-project-cost',
            title: <FormattedMessage id={sidebar + 'monthly-project-cost'} />,
            type: 'collapse',
            icon: icons.MonthlyProjectCost,
            access: MAIN_FUNCTIONS.reports.monthlyProjectCost.root,
            children: [
                {
                    id: 'summary',
                    title: <FormattedMessage id={sidebar + 'monthly-project-cost-summary'} />,
                    type: 'item',
                    url: `/${ROUTER.reports.monthly_project_cost.index}/${ROUTER.reports.monthly_project_cost.summary}`,
                    breadcrumbs: true,
                    access: MAIN_FUNCTIONS.reports.monthlyProjectCost.summary
                },
                {
                    id: 'detail-report-by-month',
                    title: <FormattedMessage id={sidebar + 'detail-report-cost-by-month'} />,
                    type: 'item',
                    url: `/${ROUTER.reports.monthly_project_cost.index}/${ROUTER.reports.monthly_project_cost.detail_report_by_month}`,
                    breadcrumbs: true,
                    access: MAIN_FUNCTIONS.reports.monthlyProjectCost.detailByMonth
                },
                {
                    id: 'monthly-cost-data',
                    title: <FormattedMessage id={sidebar + 'monthly-cost-data'} />,
                    type: 'item',
                    url: `/${ROUTER.reports.monthly_project_cost.index}/${ROUTER.reports.monthly_project_cost.monthly_cost_data}`,
                    breadcrumbs: true,
                    access: MAIN_FUNCTIONS.reports.monthlyProjectCost.monthlyCost
                }
            ]
        },
        // ============== Sales ==============
        {
            id: 'sales-report',
            title: <FormattedMessage id={sidebar + 'sales-report'} />,
            type: 'collapse',
            icon: icons.MonthlyProductionPerformance,
            access: MAIN_FUNCTIONS.sale.root,
            children: [
                {
                    id: 'monthly-production-performance',
                    title: <FormattedMessage id={sidebar + 'monthly-production-performance'} />,
                    type: 'item',
                    url: `/${ROUTER.reports.sales.index}/${ROUTER.reports.sales.monthly_production_performance}`,
                    breadcrumbs: true,
                    access: MAIN_FUNCTIONS.sale.monthlyProductionPerformance
                },
                {
                    id: 'sales-pipeline',
                    title: <FormattedMessage id={sidebar + 'sales-pipeline'} />,
                    type: 'collapse',
                    breadcrumbs: true,
                    access: MAIN_FUNCTIONS.sale.salePipeline.root,
                    children: [
                        {
                            id: 'summary',
                            title: <FormattedMessage id={sidebar + 'sales-pipeline-summary'} />,
                            type: 'item',
                            url: `/${ROUTER.reports.sales.index}/${ROUTER.reports.sales.sales_pipeline.index}/${ROUTER.reports.sales.sales_pipeline.summary}`,
                            access: MAIN_FUNCTIONS.sale.salePipeline.summary,
                            breadcrumbs: true
                        },
                        {
                            id: 'on-going',
                            title: <FormattedMessage id={sidebar + 'on-going'} />,
                            type: 'item',
                            url: `/${ROUTER.reports.sales.index}/${ROUTER.reports.sales.sales_pipeline.index}/${ROUTER.reports.sales.sales_pipeline.on_going}`,
                            access: MAIN_FUNCTIONS.sale.salePipeline.onGoing,
                            breadcrumbs: true
                        },
                        {
                            id: 'all',
                            title: <FormattedMessage id={sidebar + 'sale-salepipeline-bidding'} />,
                            type: 'item',
                            url: `/${ROUTER.reports.sales.index}/${ROUTER.reports.sales.sales_pipeline.index}/${ROUTER.reports.sales.sales_pipeline.bidding}`,
                            access: MAIN_FUNCTIONS.sale.salePipeline.bidding,
                            breadcrumbs: true
                        },
                        {
                            id: 'budgeting-plan',
                            title: <FormattedMessage id={sidebar + 'budgeting-plan'} />,
                            type: 'item',
                            url: `/${ROUTER.reports.sales.index}/${ROUTER.reports.sales.sales_pipeline.index}/${ROUTER.reports.sales.sales_pipeline.budgeting_plan}`,
                            access: MAIN_FUNCTIONS.sale.salePipeline.budget,
                            breadcrumbs: true
                        }
                    ]
                },
                {
                    id: 'sales-lead',
                    title: <FormattedMessage id={sidebar + 'sales-lead'} />,
                    type: 'item',
                    url: `/${ROUTER.reports.sales.index}/${ROUTER.reports.sales.sales_lead}`,
                    breadcrumbs: true,
                    access: MAIN_FUNCTIONS.sale.saleLead
                },
                {
                    id: 'monitor-bidding-package',
                    title: <FormattedMessage id={sidebar + 'monitor-bidding-package'} />,
                    type: 'item',
                    url: `/${ROUTER.reports.sales.index}/${ROUTER.reports.sales.monitor_bidding_package}`,
                    breadcrumbs: true,
                    access: MAIN_FUNCTIONS.sale.monitorBiddingPackage
                },
                {
                    id: 'project-reference',
                    title: <FormattedMessage id={sidebar + 'project-reference'} />,
                    type: 'item',
                    url: `/${ROUTER.reports.sales.index}/${ROUTER.reports.sales.project_reference}`,
                    breadcrumbs: true,
                    access: MAIN_FUNCTIONS.sale.projectReference
                },
                {
                    id: 'payment-status',
                    title: <FormattedMessage id={sidebar + 'payment-status'} />,
                    type: 'item',
                    url: `/${ROUTER.reports.sales.index}/${ROUTER.reports.sales.payment_status}`,
                    breadcrumbs: true,
                    access: MAIN_FUNCTIONS.sale.paymentStatus
                }
            ]
        },

        // ============== Skill Manage ==============
        {
            id: 'manage-skills',
            title: <FormattedMessage id={sidebar + 'manage-skills'} />,
            type: 'collapse',
            access: MAIN_FUNCTIONS.reports.skillManage.root,
            icon: icons.ManageSkills,
            children: [
                {
                    id: 'skills-update',
                    title: <FormattedMessage id={sidebar + 'skills-update'} />,
                    type: 'item',
                    url: `/${ROUTER.reports.skills_manage.index}/${ROUTER.reports.skills_manage.skills_update}`,
                    access: MAIN_FUNCTIONS.reports.skillManage.skillsUpdate,
                    breadcrumbs: true
                },
                {
                    id: 'skills-report',
                    title: <FormattedMessage id={sidebar + 'skills-report'} />,
                    type: 'item',
                    url: `/${ROUTER.reports.skills_manage.index}/${ROUTER.reports.skills_manage.skills_report}`,
                    access: MAIN_FUNCTIONS.reports.skillManage.skillsReport,
                    breadcrumbs: true
                }
            ]
        }
    ]
};

export default reports;
