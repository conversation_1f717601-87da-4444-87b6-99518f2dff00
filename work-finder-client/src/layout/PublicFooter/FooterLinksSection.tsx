import { Link } from 'react-router-dom';

// material-ui
import { Grid, Typography, Box, useTheme } from '@mui/material';

// ==============================|| FOOTER LINKS SECTION ||============================== //

const FooterLinksSection = () => {
    const theme = useTheme();

    const footerLinks = {
        forCandidates: {
            title: 'For Candidates',
            links: [
                { title: 'Browse Jobs', path: '/jobs' },
                { title: 'Browse Categories', path: '/categories' },
                { title: 'Candidate Dashboard', path: '/candidate/dashboard' },
                { title: 'Job Alerts', path: '/job-alerts' },
                { title: 'My Bookmarks', path: '/bookmarks' }
            ]
        },
        forEmployers: {
            title: 'For Employers',
            links: [
                { title: 'Browse Candidates', path: '/candidates' },
                { title: 'Employer Dashboard', path: '/employer/dashboard' },
                { title: 'Add Job', path: '/post-job' },
                { title: 'Job Packages', path: '/packages' }
            ]
        },
        aboutUs: {
            title: 'About Us',
            links: [
                { title: 'Job Page', path: '/job-page' },
                { title: 'Job Page Alternative', path: '/job-page-alt' },
                { title: 'Resume Page', path: '/resume-page' },
                { title: 'Blog', path: '/blog' },
                { title: 'Contact', path: '/contact' }
            ]
        },
        helpfulResources: {
            title: 'Helpful Resources',
            links: [
                { title: 'Site Map', path: '/sitemap' },
                { title: 'Terms of Use', path: '/terms' },
                { title: 'Privacy Center', path: '/privacy' },
                { title: 'Security Center', path: '/security' },
                { title: 'Accessibility Center', path: '/accessibility' }
            ]
        }
    };

    const renderLinkColumn = (columnData: { title: string; links: { title: string; path: string }[] }) => (
        <Grid item xs={12} sm={6} md={3}>
            <Typography
                variant="h6"
                sx={{
                    fontWeight: 500,
                    fontSize: 18,
                    color: theme.palette.text.primary,
                    mb: 3
                }}
            >
                {columnData.title}
            </Typography>
            <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2.5 }}>
                {columnData.links.map((link) => (
                    <Link
                        key={link.title}
                        to={link.path}
                        style={{
                            color: theme.palette.text.secondary,
                            textDecoration: 'none',
                            fontWeight: 400,
                            fontSize: 14,
                            lineHeight: 2.5,
                            transition: 'color 0.2s ease'
                        }}
                        onMouseEnter={(e) => {
                            e.currentTarget.style.color = theme.palette.primary.main;
                        }}
                        onMouseLeave={(e) => {
                            e.currentTarget.style.color = theme.palette.text.secondary;
                        }}
                    >
                        {link.title}
                    </Link>
                ))}
            </Box>
        </Grid>
    );

    return (
        <Grid container spacing={4}>
            {renderLinkColumn(footerLinks.forCandidates)}
            {renderLinkColumn(footerLinks.forEmployers)}
            {renderLinkColumn(footerLinks.aboutUs)}
            {renderLinkColumn(footerLinks.helpfulResources)}
        </Grid>
    );
};

export default FooterLinksSection;
