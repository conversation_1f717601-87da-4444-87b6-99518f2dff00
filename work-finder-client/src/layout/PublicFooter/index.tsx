// material-ui
import { Box, Container, Grid, Divider, IconButton, useTheme } from '@mui/material';
import ExpandLessIcon from '@mui/icons-material/ExpandLess';

// project imports
import CompanyInfoSection from './CompanyInfoSection';
import FooterLinksSection from './FooterLinksSection';
import BottomSection from './BottomSection';

// ==============================|| PUBLIC FOOTER ||============================== //

const PublicFooter = () => {
    const theme = useTheme();

    return (
        <Box
            component="footer"
            sx={{
                backgroundColor: '#F0F5F7',
                py: 6,
                mt: 'auto'
            }}
        >
            <Container maxWidth="xl">
                <Grid container spacing={4} sx={{ mb: 4 }}>
                    {/* Company Info Section */}
                    <Grid item xs={12} md={3}>
                        <CompanyInfoSection />
                    </Grid>

                    {/* Footer Links Section */}
                    <Grid item xs={12} md={9}>
                        <FooterLinksSection />
                    </Grid>
                </Grid>

                {/* Divider */}
                <Divider sx={{ backgroundColor: theme.palette.divider, height: 1, mb: 4 }} />

                {/* Bottom Section */}
                <BottomSection />
            </Container>

            {/* Up Arrow Button */}
            <Box
                sx={{
                    position: 'fixed',
                    bottom: 30,
                    right: 30,
                    zIndex: 1000
                }}
            >
                <IconButton
                    sx={{
                        width: 40,
                        height: 40,
                        backgroundColor: theme.palette.primary.main,
                        color: theme.palette.primary.contrastText,
                        opacity: 0.07,
                        '&:hover': {
                            opacity: 0.12,
                            backgroundColor: theme.palette.primary.main
                        }
                    }}
                    onClick={() => window.scrollTo({ top: 0, behavior: 'smooth' })}
                >
                    <ExpandLessIcon />
                </IconButton>
            </Box>
        </Box>
    );
};

export default PublicFooter;
