import { useState, useEffect } from 'react';

// material-ui
import { AppBar, Box, Container, useTheme, useMediaQuery } from '@mui/material';

// project imports
import LogoSection from '../LogoSection';
import NavigationSection from './NavigationSection';
import UserActionsSection from './UserActionsSection';
import MobileSection from './MobileSection';

// ==============================|| PUBLIC HEADER ||============================== //

const PublicHeader = () => {
    const theme = useTheme();
    const isMobile = useMediaQuery(theme.breakpoints.down('md'));
    const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
    const [isScrolled, setIsScrolled] = useState(false);

    const handleMobileMenuToggle = () => {
        setMobileMenuOpen(!mobileMenuOpen);
    };

    useEffect(() => {
        const handleScroll = () => {
            const scrollTop = window.scrollY;
            setIsScrolled(scrollTop > 200);
        };

        window.addEventListener('scroll', handleScroll);
        return () => window.removeEventListener('scroll', handleScroll);
    }, []);

    return (
        <AppBar
            position="fixed"
            elevation={0}
            sx={{
                backgroundColor: isMobile ? '#FFFFFF' : isScrolled ? '#FFFFFF' : 'transparent',
                minHeight: { xs: '80px', md: '64px' },
                borderBottom: 'none',
                boxShadow: isMobile || isScrolled ? '0 2px 8px rgba(0, 0, 0, 0.1)' : 'none',
                transition: 'all 0.3s ease-in-out',
                zIndex: theme.zIndex.appBar
            }}
        >
            <Container maxWidth="xl">
                <Box
                    sx={{
                        height: { xs: '80px', md: '64px' },
                        minHeight: { xs: '80px', md: '64px' },
                        display: 'flex',
                        alignItems: 'center'
                    }}
                >
                    {/* Logo Section - Always on left */}
                    <Box
                        sx={{
                            display: 'flex',
                            alignItems: 'center',
                            // mr: { xs: 0, md: 4 },
                            flexGrow: { xs: 1, md: 0 },
                            justifyContent: 'flex-start'
                        }}
                    >
                        <LogoSection />
                    </Box>

                    {/* Navigation Section - Desktop Only */}
                    <Box sx={{ flexGrow: 1, display: { xs: 'none', md: 'flex' }, ml: 2 }}>
                        <NavigationSection />
                    </Box>

                    {/* User Actions Section - Desktop Only */}
                    <Box sx={{ display: { xs: 'none', md: 'flex' }, flexGrow: 0 }}>
                        <UserActionsSection />
                    </Box>

                    {/* Mobile Menu Button - Always on right */}
                    <Box sx={{ display: { xs: 'flex', md: 'none' } }}>
                        <MobileSection open={mobileMenuOpen} onToggle={handleMobileMenuToggle} />
                    </Box>
                </Box>
            </Container>

            {/* Mobile Menu Content - Outside Toolbar to push content down */}
            <Box sx={{ display: { xs: 'block', md: 'none' } }}>
                <MobileSection open={mobileMenuOpen} onToggle={handleMobileMenuToggle} contentOnly />
            </Box>
        </AppBar>
    );
};

export default PublicHeader;
