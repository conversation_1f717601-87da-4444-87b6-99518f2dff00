import { useNavigate, useLocation } from 'react-router-dom';

// material-ui
import { Link, useTheme } from '@mui/material';

// constants
import mainNavigation, { isActiveNavItem } from 'constants/MainNavigation';

// ==============================|| NAVIGATION SECTION ||============================== //

const NavigationSection = () => {
    const theme = useTheme();
    const navigate = useNavigate();
    const location = useLocation();

    const handleNavigate = (path: string) => {
        navigate(path);
    };

    return (
        <>
            {mainNavigation.map((item) => {
                const isActive = isActiveNavItem(location.pathname, item.path);

                return (
                    <Link
                        key={item.id}
                        onClick={() => handleNavigate(item.path)}
                        sx={{
                            fontWeight: 400,
                            fontSize: 15,
                            color: isActive ? theme.palette.primary.main : '#202124',
                            textDecoration: 'none',
                            cursor: 'pointer',
                            mx: 2,
                            py: 1,
                            '&:hover': {
                                color: theme.palette.primary.main
                            }
                        }}
                    >
                        {item.title}
                    </Link>
                );
            })}
        </>
    );
};

export default NavigationSection;
