import { createRef, forwardRef, useImperativeHandle, useState } from 'react';
import { Link, Outlet } from 'react-router-dom';

// material-ui
import { Box, Stack, Typography, useMediaQuery } from '@mui/material';
import { useTheme } from '@mui/material/styles';

// project imports
import Logo from 'components/Logo';
import leftImage from 'assets/images/auth/left.png';

interface IAuthLayoutRef {
    setState: ({ title, maxWidth }: { title: string; maxWidth?: number }) => void;
}

export const authLayoutRef = createRef<IAuthLayoutRef>();

const AuthLayout = forwardRef<IAuthLayoutRef>((_, ref) => {
    const theme = useTheme();

    useImperativeHandle<IAuthLayoutRef, IAuthLayoutRef>(ref, () => ({
        setState() {
            // No longer needed since each page manages its own content
        }
    }));

    return (
        <Box sx={{ display: 'flex', minHeight: '100vh' }}>
            {/* Logo - Fixed at top-left corner */}
            <Box
                sx={{
                    position: 'fixed',
                    top: 24,
                    left: 24,
                    zIndex: 1000,
                    display: { xs: 'none', md: 'block' }
                }}
            >
                <Link to="/" style={{ textDecoration: 'none' }}>
                    <Logo width={150} height={45} />
                </Link>
            </Box>

            {/* Left Column - Fixed Image (40%) */}
            <Box
                sx={{
                    position: 'fixed',
                    top: 0,
                    left: 0,
                    width: { xs: '100%', md: '40%' },
                    height: '100vh',
                    display: { xs: 'none', md: 'block' },
                    overflow: 'hidden'
                }}
            >
                <img
                    src={leftImage}
                    alt="Work Finder Illustration"
                    style={{
                        width: '100%',
                        height: '100%',
                        objectFit: 'cover'
                    }}
                />
            </Box>

            {/* Right Column - Scrollable Content (60%) */}
            <Box
                sx={{
                    marginLeft: { xs: 0, md: '40%' },
                    width: { xs: '100%', md: '60%' },
                    minHeight: '100vh',
                    backgroundColor: theme.palette.background.default,
                    overflowY: 'auto'
                }}
            >
                {/* Mobile Logo */}
                <Box
                    sx={{
                        display: { xs: 'flex', md: 'none' },
                        justifyContent: 'center',
                        pt: 3,
                        pb: 2
                    }}
                >
                    <Link to="/" style={{ textDecoration: 'none' }}>
                        <Logo width={150} height={45} />
                    </Link>
                </Box>

                {/* Page Content */}
                <Box
                    sx={{
                        display: 'flex',
                        flexDirection: 'column',
                        justifyContent: 'center',
                        alignItems: 'center',
                        minHeight: { xs: 'calc(100vh - 80px)', md: '100vh' },
                        p: { xs: 3, sm: 4, md: 6 }
                    }}
                >
                    <Box
                        sx={{
                            width: '100%',
                            maxWidth: 480,
                            display: 'flex',
                            flexDirection: 'column',
                            justifyContent: 'center'
                        }}
                    >
                        <Outlet />
                    </Box>
                </Box>
            </Box>
        </Box>
    );
});

export default AuthLayout;
