import { useTheme } from '@mui/material/styles';
import {
    SPACING,
    BORDER_RADIUS,
    CONTAINER_WIDTHS,
    COMPONENT_SPACING,
    RESPONSIVE_SPACING,
    type SpacingKey,
    type BorderRadiusKey,
    type ContainerWidthKey
} from '../constants/Spacing';

/**
 * Custom hook for consistent spacing throughout the application
 * Provides easy access to spacing values with TypeScript support
 */
export const useSpacing = () => {
    const theme = useTheme();

    // Get spacing value in pixels
    const spacing = (key: SpacingKey): number => SPACING[key];

    // Get spacing value as CSS string
    const spacingPx = (key: SpacingKey): string => `${SPACING[key]}px`;

    // Get border radius value
    const borderRadius = (key: BorderRadiusKey): number => BORDER_RADIUS[key];

    // Get border radius as CSS string
    const borderRadiusPx = (key: BorderRadiusKey): string => (key === 'full' ? `${BORDER_RADIUS[key]}px` : `${BORDER_RADIUS[key]}px`);

    // Get container width
    const containerWidth = (key: ContainerWidthKey): string => CONTAINER_WIDTHS[key];

    // Get responsive spacing based on current breakpoint
    const getResponsiveSpacing = (spacingType: 'container' | 'section' = 'container'): string => {
        const breakpoints = theme.breakpoints;
        const responsiveValues = RESPONSIVE_SPACING[spacingType];

        if (breakpoints.up('xl')) return spacingPx('5xl');
        if (breakpoints.up('lg')) return spacingPx('3xl');
        if (breakpoints.up('md')) return spacingPx('2xl');
        if (breakpoints.up('sm')) return spacingPx('xl');
        return spacingPx('md');
    };

    // Create responsive spacing object for sx prop
    const createResponsiveSpacing = (xs: SpacingKey, sm?: SpacingKey, md?: SpacingKey, lg?: SpacingKey, xl?: SpacingKey) => ({
        xs: spacingPx(xs),
        ...(sm && { sm: spacingPx(sm) }),
        ...(md && { md: spacingPx(md) }),
        ...(lg && { lg: spacingPx(lg) }),
        ...(xl && { xl: spacingPx(xl) })
    });

    // Get spacing for specific component type
    const componentSpacing = {
        header: COMPONENT_SPACING.header,
        card: COMPONENT_SPACING.card,
        layout: COMPONENT_SPACING.layout,
        form: COMPONENT_SPACING.form,
        list: COMPONENT_SPACING.list,
        button: COMPONENT_SPACING.button,
        icon: COMPONENT_SPACING.icon
    };

    // Common spacing patterns
    const patterns = {
        // Standard padding patterns
        cardPadding: spacingPx('3xl'), // 24px
        containerPadding: spacingPx('5xl'), // 40px
        sectionPadding: spacingPx('4xl'), // 32px

        // Standard margin patterns
        elementMargin: spacingPx('xl'), // 16px
        componentMargin: spacingPx('2xl'), // 20px
        sectionMargin: spacingPx('4xl'), // 32px

        // Standard gap patterns
        smallGap: spacingPx('md'), // 8px
        mediumGap: spacingPx('lg'), // 12px
        largeGap: spacingPx('xl'), // 16px

        // Border radius patterns
        inputRadius: borderRadiusPx('sm'), // 4px
        cardRadius: borderRadiusPx('md'), // 8px
        buttonRadius: borderRadiusPx('sm'), // 4px
        badgeRadius: borderRadiusPx('full') // 9999px
    };

    // Helper function to create consistent box spacing
    const createBoxSpacing = (padding?: SpacingKey, margin?: SpacingKey, gap?: SpacingKey) => ({
        ...(padding && { padding: spacingPx(padding) }),
        ...(margin && { margin: spacingPx(margin) }),
        ...(gap && { gap: spacingPx(gap) })
    });

    // Helper function for form field spacing
    const createFormFieldSpacing = () => ({
        marginBottom: spacingPx('xl'), // 16px between fields
        '& .MuiFormLabel-root': {
            marginBottom: spacingPx('md') // 8px label gap
        },
        '& .MuiFormHelperText-root': {
            marginTop: spacingPx('sm') // 4px helper text gap
        }
    });

    // Helper function for list spacing
    const createListSpacing = (itemGap: SpacingKey = 'lg', groupGap: SpacingKey = '2xl') => ({
        '& .MuiListItem-root': {
            marginBottom: spacingPx(itemGap),
            '&:last-child': {
                marginBottom: 0
            }
        },
        '& .MuiListSubheader-root': {
            marginTop: spacingPx(groupGap),
            '&:first-of-type': {
                marginTop: 0
            }
        }
    });

    return {
        // Basic spacing functions
        spacing,
        spacingPx,
        borderRadius,
        borderRadiusPx,
        containerWidth,

        // Responsive utilities
        getResponsiveSpacing,
        createResponsiveSpacing,

        // Component spacing presets
        componentSpacing,

        // Common patterns
        patterns,

        // Helper functions
        createBoxSpacing,
        createFormFieldSpacing,
        createListSpacing,

        // Direct access to constants
        constants: {
            SPACING,
            BORDER_RADIUS,
            CONTAINER_WIDTHS,
            COMPONENT_SPACING,
            RESPONSIVE_SPACING
        }
    };
};

export default useSpacing;
