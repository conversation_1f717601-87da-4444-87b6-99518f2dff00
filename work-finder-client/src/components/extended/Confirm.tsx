import { useState } from 'react';
import { useAppDispatch, useAppSelector } from 'app/hooks';

// material ui
import { LoadingButton } from '@mui/lab';
import { Box, Button, Dialog, Stack, Typography } from '@mui/material';

// project imports
import MainCard from 'components/cards/MainCard';
import { closeConfirm, confirmSelector } from 'store/slice/confirmSlice';

// third party
import { FormattedMessage } from 'react-intl';

const Confirm = () => {
    const [loading, setLoading] = useState<boolean>(false);

    const confirm = useAppSelector(confirmSelector);
    const dispatch = useAppDispatch();

    const handleConfirm = async () => {
        setLoading(true);
        await confirm.handleConfirm();
        setLoading(false);
    };

    return (
        <Dialog
            keepMounted={false}
            open={confirm.open}
            onClose={() => dispatch(closeConfirm())}
            maxWidth="xs"
            sx={{
                '& .MuiDialog-paper': { p: 0, width: confirm.width }
            }}
        >
            <MainCard title={confirm.title}>
                <Box sx={{ mb: '20px' }}>
                    <Typography variant="subtitle1">{confirm.content}</Typography>
                </Box>
                <Stack direction="row" spacing={1} justifyContent="end">
                    <Button color="error" onClick={() => dispatch(closeConfirm())} disabled={loading}>
                        <FormattedMessage id="cancel" />
                    </Button>
                    <LoadingButton variant="contained" type="submit" loading={loading} onClick={handleConfirm}>
                        <FormattedMessage id="confirm" />
                    </LoadingButton>
                </Stack>
            </MainCard>
        </Dialog>
    );
};
export default Confirm;
