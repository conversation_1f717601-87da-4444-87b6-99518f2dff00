const WorkingCalendarClosingDate = () => {
    return (
        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M8 2V5" stroke="#3163D4" strokeWidth="1.5" strokeMiterlimit="10" strokeLinecap="round" strokeLinejoin="round" />
            <path d="M16 2V5" stroke="#3163D4" strokeWidth="1.5" strokeMiterlimit="10" strokeLinecap="round" strokeLinejoin="round" />
            <path
                d="M3.5 9.08997H20.5"
                stroke="#3163D4"
                strokeWidth="1.5"
                strokeMiterlimit="10"
                strokeLinecap="round"
                strokeLinejoin="round"
            />
            <path
                d="M21 12.5V8.5C21 5.5 19.5 3.5 16 3.5H8C4.5 3.5 3 5.5 3 8.5V17C3 20 4.5 22 8 22H11.5"
                stroke="#3163D4"
                strokeWidth="1.5"
                strokeMiterlimit="10"
                strokeLinecap="round"
                strokeLinejoin="round"
            />
            <path d="M11.9955 13.7H12.0045" stroke="#3163D4" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
            <path d="M8.29431 13.7H8.30329" stroke="#3163D4" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
            <path d="M8.29431 16.7H8.30329" stroke="#3163D4" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
            <path
                d="M15.6 17.2V16.4C15.6 15.07 16 14 18 14C20 14 20.4 15.07 20.4 16.4V17.2"
                stroke="#3163D4"
                strokeWidth="1.5"
                strokeLinecap="round"
                strokeLinejoin="round"
            />
            <path
                d="M20 22H16C14.4 22 14 21.6 14 20V19.2C14 17.6 14.4 17.2 16 17.2H20C21.6 17.2 22 17.6 22 19.2V20C22 21.6 21.6 22 20 22Z"
                stroke="#3163D4"
                strokeWidth="1.5"
                strokeLinecap="round"
                strokeLinejoin="round"
            />
        </svg>
    );
};

export default WorkingCalendarClosingDate;
