const PieChart = () => {
    return (
        <svg version="1.1" width="38px" height="35px">
            <g transform="matrix(1 0 0 1 -843 -50 )">
                <path
                    d="M 17 2.6923076923076907  L 17 18.71995192307692  L 28.895833333333332 30.20432692307692  C 27.341820987654323 31.71875  25.527584876543212 32.89663461538461  23.453125 33.73798076923077  C 21.37866512345679 34.57932692307693  19.190586419753085 35  16.88888888888889 35  C 13.824845679012347 35  10.999035493827162 34.27784455128205  8.411458333333332 32.83353365384615  C 5.823881172839507 31.389222756410255  3.7750771604938276 29.42958733974359  2.2650462962962963 26.954627403846153  C 0.7550154320987653 24.479667467948715  0 21.77684294871795  0 18.846153846153847  C 0 15.91546474358974  0.7550154320987653 13.21264022435897  2.2650462962962963 10.73768028846154  C 3.7750771604938276 8.262720352564102  5.823881172839507 6.303084935897434  8.411458333333332 4.85877403846154  C 10.999035493827162 3.414463141025638  13.824845679012347 2.6923076923076907  17 2.6923076923076907  Z M 32.986111111111114 30.330528846153847  L 21.00115740740741 18.846153846153847  L 38 18.846153846153847  C 38.00000000000001 21.047676282051285  37.56018518518519 23.140524839743584  36.68055555555556 25.12469951923077  C 35.80092592592593 27.10887419871795  34.56944444444445 28.84415064102564  32.986111111111114 30.330528846153847  Z M 34.3275462962963 8.045372596153845  C 35.83757716049383 10.52033253205128  36.59259259259259 13.22315705128205  36.59259259259259 16.153846153846153  L 19 16.153846153846153  L 19 0  C 22.76774691358025 0  25.593557098765434 0.722155448717951  28.18113425925926 2.1664663461538454  C 30.768711419753085 3.6107772435897396  32.817515432098766 5.570412660256407  34.3275462962963 8.045372596153845  Z "
                    fillRule="nonzero"
                    fill="#3163d4"
                    stroke="none"
                    transform="matrix(1 0 0 1 843 50 )"
                />
            </g>
        </svg>
    );
};

export default PieChart;
