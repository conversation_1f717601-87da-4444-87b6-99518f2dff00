// material-ui
import { SvgIcon, SvgIconProps } from '@mui/material';

// ===============================|| MONEY ICON ||=============================== //

const MoneyIcon = (props: SvgIconProps) => {
    return (
        <SvgIcon {...props} viewBox="0 0 35 35">
            <g clipPath="url(#clip0_32_92)">
                <path d="M23.3758 22.4464C22.5465 22.4464 21.8719 21.8625 21.8719 21.1448C21.8719 20.427 22.5465 19.8431 23.3758 19.8431C24.205 19.8431 24.8797 20.427 24.8797 21.1448C24.8797 21.5222 25.1858 21.8284 25.5633 21.8284C25.9407 21.8284 26.2468 21.5222 26.2468 21.1448C26.2468 19.8921 25.3135 18.8388 24.0594 18.5529V18.1974C24.0594 17.8199 23.7532 17.5138 23.3758 17.5138C22.9983 17.5138 22.6922 17.8199 22.6922 18.1974V18.5529C21.438 18.8388 20.5047 19.8921 20.5047 21.1448C20.5047 22.6163 21.7926 23.8136 23.3758 23.8136C24.205 23.8136 24.8797 24.3975 24.8797 25.1153C24.8797 25.833 24.205 26.417 23.3758 26.417C22.5465 26.417 21.8719 25.833 21.8719 25.1153C21.8719 24.7378 21.5657 24.4317 21.1883 24.4317C20.8108 24.4317 20.5047 24.7378 20.5047 25.1153C20.5047 26.368 21.438 27.4212 22.6922 27.7071V28.0024C22.6922 28.3799 22.9983 28.686 23.3758 28.686C23.7532 28.686 24.0594 28.3799 24.0594 28.0024V27.7071C25.3135 27.4212 26.2468 26.368 26.2468 25.1153C26.2468 23.6436 24.9589 22.4464 23.3758 22.4464Z" />
                <path d="M30.863 18.3801C29.97 16.9887 28.712 15.8752 27.2248 15.1603C26.8844 14.9967 26.476 15.14 26.3125 15.4803C26.1489 15.8206 26.2921 16.2289 26.6324 16.3925C29.224 17.6383 30.8984 20.3001 30.8984 23.1738C30.8984 24.4035 30.5953 25.6229 30.022 26.7006C29.8447 27.0338 29.9711 27.4478 30.3044 27.6251C30.4067 27.6795 30.5166 27.7054 30.6249 27.7054C30.8694 27.7054 31.106 27.5737 31.2289 27.3427C31.9071 26.0682 32.2656 24.6266 32.2656 23.1738C32.2656 21.4676 31.7806 19.8099 30.863 18.3801Z" />
                <path d="M24.4502 14.2873L24.4042 14.2872C24.0268 14.2872 23.7206 14.5933 23.7206 14.9708C23.7206 15.3484 24.0268 15.6544 24.4042 15.6544L24.4438 15.6545C24.4449 15.6545 24.4459 15.6545 24.4471 15.6545C24.8232 15.6545 25.1289 15.3506 25.1306 14.9741C25.1323 14.5966 24.8277 14.2891 24.4502 14.2873Z" />
                <path d="M20.1254 29.9552C17.5339 28.7093 15.8595 26.0475 15.8595 23.1739C15.8595 21.9442 16.1626 20.7247 16.7359 19.6471C16.9132 19.3139 16.7868 18.8999 16.4535 18.7226C16.1203 18.5453 15.7062 18.6716 15.5289 19.005C14.8508 20.2795 14.4923 21.7211 14.4923 23.1739C14.4923 24.8801 14.9773 26.5377 15.8949 27.9675C16.7878 29.359 18.0459 30.4724 19.5331 31.1874C19.6286 31.2333 19.7295 31.255 19.8288 31.255C20.0834 31.255 20.3277 31.1122 20.4454 30.8675C20.6089 30.5272 20.4657 30.1187 20.1254 29.9552Z" />
                <path d="M22.3536 30.6933L22.314 30.6931C21.9348 30.6924 21.6291 30.9959 21.6274 31.3735C21.6256 31.7511 21.9303 32.0585 22.3078 32.0603L22.3536 32.0605C22.7311 32.0605 23.0372 31.7543 23.0372 31.3769C23.0372 30.9994 22.7311 30.6933 22.3536 30.6933Z" />
                <path d="M26.483 11.9744C26.3998 11.7761 26.2979 11.5893 26.1793 11.416H27.9589C29.6928 11.416 31.1035 10.0053 31.1035 8.27147C31.1035 6.53761 29.6928 5.12694 27.9589 5.12694H22.4847C22.8327 4.62095 23.0371 4.00879 23.0371 3.3496C23.0371 1.61574 21.6264 0.205078 19.8925 0.205078H3.14453C1.41066 0.205078 0 1.61574 0 3.3496C0 5.08347 1.41066 6.49413 3.14453 6.49413H8.61873C8.27072 7.00012 8.06639 7.61228 8.06639 8.27147C8.06639 8.93066 8.27065 9.54282 8.61873 10.0488H6.83592C5.10206 10.0488 3.6914 11.4595 3.6914 13.1933C3.6914 14.1891 4.1572 15.0777 4.88188 15.6543C4.1572 16.2308 3.6914 17.1194 3.6914 18.1152C3.6914 19.111 4.1572 19.9996 4.88188 20.5761C4.1572 21.1527 3.6914 22.0413 3.6914 23.0371C3.6914 24.0329 4.1572 24.9215 4.88188 25.498C4.1572 26.0745 3.6914 26.9631 3.6914 27.9589C3.6914 29.6928 5.10206 31.1035 6.83592 31.1035H14.8921C17.0143 33.3735 20.0335 34.7949 23.3789 34.7949C29.7868 34.7949 35 29.5817 35 23.1739C35 17.8407 31.3886 13.3357 26.483 11.9744ZM27.9589 6.49413C28.939 6.49413 29.7363 7.29147 29.7363 8.27147C29.7363 9.25147 28.939 10.0488 27.9589 10.0488H23.5839H11.2109C10.2309 10.0488 9.43358 9.25147 9.43358 8.27147C9.43358 7.29147 10.2309 6.49413 11.2109 6.49413H27.9589ZM3.14453 5.12694C2.16446 5.12694 1.36718 4.3296 1.36718 3.3496C1.36718 2.36961 2.16446 1.57226 3.14453 1.57226H19.8925C20.8726 1.57226 21.6699 2.36961 21.6699 3.3496C21.6699 4.3296 20.8726 5.12694 19.8925 5.12694H3.14453ZM6.83592 11.416H23.5839C23.8593 11.416 24.123 11.48 24.3595 11.5945C24.0361 11.5674 23.7092 11.5528 23.3789 11.5528C20.1698 11.5528 17.2604 12.8604 15.1554 14.9707H6.83592C5.85593 14.9707 5.05858 14.1733 5.05858 13.1933C5.05858 12.2133 5.85593 11.416 6.83592 11.416ZM6.83592 16.3379H13.9873C13.2113 17.4011 12.6113 18.6002 12.2303 19.8925H6.83592C5.85586 19.8925 5.05858 19.0953 5.05858 18.1152C5.05858 17.1352 5.85586 16.3379 6.83592 16.3379ZM6.83592 21.2597H11.9164C11.8127 21.8827 11.7579 22.5219 11.7579 23.1739C11.7579 23.7307 11.7981 24.2783 11.8742 24.8144H6.83592C5.85586 24.8144 5.05858 24.0171 5.05858 23.0371C5.05858 22.057 5.85586 21.2597 6.83592 21.2597ZM6.83592 29.7363C5.85586 29.7363 5.05858 28.939 5.05858 27.9589C5.05858 26.9789 5.85586 26.1816 6.83592 26.1816H12.1533C12.4983 27.4676 13.0586 28.6662 13.7935 29.7363H6.83592ZM23.3789 33.4277C17.7249 33.4277 13.125 28.8278 13.125 23.1739C13.125 17.5199 17.7249 12.92 23.3789 12.92C29.0329 12.92 33.6328 17.5199 33.6328 23.1739C33.6328 28.8278 29.0329 33.4277 23.3789 33.4277Z" />
            </g>
            <defs>
                <clipPath id="clip0_32_92">
                    <rect width="35" height="35" fill="white" />
                </clipPath>
            </defs>
        </SvgIcon>
    );
};

export default MoneyIcon;
