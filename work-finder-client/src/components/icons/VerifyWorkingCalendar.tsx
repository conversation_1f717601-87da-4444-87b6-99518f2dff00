const VerifyWorkingCalendar = () => {
    return (
        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M8 2V5" stroke="#3163D4" strokeWidth="1.5" strokeMiterlimit="10" strokeLinecap="round" strokeLinejoin="round" />
            <path d="M16 2V5" stroke="#3163D4" strokeWidth="1.5" strokeMiterlimit="10" strokeLinecap="round" strokeLinejoin="round" />
            <path
                d="M3.5 9.08997H20.5"
                stroke="#3163D4"
                strokeWidth="1.5"
                strokeMiterlimit="10"
                strokeLinecap="round"
                strokeLinejoin="round"
            />
            <path
                d="M21 12.5V8.5C21 5.5 19.5 3.5 16 3.5H8C4.5 3.5 3 5.5 3 8.5V17C3 20 4.5 22 8 22H11.5"
                stroke="#3163D4"
                strokeWidth="1.5"
                strokeMiterlimit="10"
                strokeLinecap="round"
                strokeLinejoin="round"
            />
            <path d="M11.9955 13.7H12.0045" stroke="#3163D4" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
            <path d="M8.29431 13.7H8.30329" stroke="#3163D4" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
            <path d="M8.29431 16.7H8.30329" stroke="#3163D4" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
            <path
                d="M22 19C22 19.75 21.79 20.46 21.42 21.06C20.73 22.22 19.46 23 18 23C16.54 23 15.27 22.22 14.58 21.06C14.21 20.46 14 19.75 14 19C14 16.79 15.79 15 18 15C20.21 15 22 16.79 22 19Z"
                stroke="#3163D4"
                strokeWidth="1.5"
                strokeMiterlimit="10"
                strokeLinecap="round"
                strokeLinejoin="round"
            />
            <path
                d="M16.4414 18.9995L17.4314 19.9895L19.5614 18.0195"
                stroke="#3163D4"
                strokeWidth="1.5"
                strokeLinecap="round"
                strokeLinejoin="round"
            />
        </svg>
    );
};

export default VerifyWorkingCalendar;
