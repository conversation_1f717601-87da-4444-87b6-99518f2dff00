import { useEffect, useState } from 'react';
import { Grid, SelectChangeEvent, Box } from '@mui/material';
import { useFormContext } from 'react-hook-form';

import { DatePicker, Input, NumericFormatCustom, PercentageFormat, Select } from 'components/extended/Form';
import { Inequalities_Condition, PERCENT_PLACEHOLDER } from 'constants/Common';

type InequalitiesConditionProps = {
    conditionName: string;
    conditionValueName: string;
    minValueName: string;
    maxValueName: string;
    isPercentage?: boolean;
    isDatePicker?: boolean;
};

const InequalitiesCondition = ({
    conditionName,
    conditionValueName,
    minValueName,
    maxValueName,
    isPercentage,
    isDatePicker
}: InequalitiesConditionProps) => {
    const [isBetweenValue, setIsBetweenValue] = useState(false);

    const method = useFormContext();

    const handleChangeCondition = (e: SelectChangeEvent<unknown>) => {
        const today = new Date();
        if (e.target.value === 'between') {
            setIsBetweenValue(true);
            method.setValue(minValueName, isDatePicker ? today : 0);
            method.setValue(maxValueName, isDatePicker ? today.setDate(today.getDate() + 1) : 1);
            method.setValue(conditionValueName, undefined);
        } else {
            setIsBetweenValue(false);
            method.setValue(
                conditionValueName,
                isDatePicker ? today : method.getValues(conditionValueName) !== undefined ? method.getValues(conditionValueName) : ''
            );
            method.setValue(minValueName, undefined);
            method.setValue(maxValueName, undefined);
        }
    };

    useEffect(() => {
        if (method.getValues(conditionName) === 'between') {
            setIsBetweenValue(true);
        } else {
            setIsBetweenValue(false);
        }
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [conditionName, method, isDatePicker]);

    return (
        <Grid container spacing={1}>
            <Grid item xs={3}>
                <Select name={conditionName} selects={Inequalities_Condition} handleChange={handleChangeCondition} />
            </Grid>

            {isDatePicker ? (
                <>
                    {isBetweenValue && minValueName && maxValueName ? (
                        <>
                            <Grid item xs={4.5}>
                                <DatePicker name={minValueName} />
                            </Grid>
                            <Grid item xs={4.5}>
                                <DatePicker name={maxValueName} />
                            </Grid>
                        </>
                    ) : (
                        <Grid item xs={4.5}>
                            {/* make it unique */}
                            <Box>
                                <DatePicker name={conditionValueName} />
                            </Box>
                        </Grid>
                    )}
                </>
            ) : (
                <>
                    {isBetweenValue && minValueName && maxValueName ? (
                        <>
                            <Grid item xs={4.5}>
                                <Input
                                    textFieldProps={
                                        isPercentage
                                            ? {
                                                  placeholder: PERCENT_PLACEHOLDER,
                                                  InputProps: {
                                                      inputComponent: PercentageFormat as any
                                                  }
                                              }
                                            : {
                                                  InputProps: {
                                                      inputComponent: NumericFormatCustom as any
                                                  }
                                              }
                                    }
                                    name={minValueName}
                                />
                            </Grid>
                            <Grid item xs={4.5}>
                                <Input
                                    textFieldProps={
                                        isPercentage
                                            ? {
                                                  placeholder: PERCENT_PLACEHOLDER,
                                                  InputProps: {
                                                      inputComponent: PercentageFormat as any
                                                  }
                                              }
                                            : {
                                                  InputProps: {
                                                      inputComponent: NumericFormatCustom as any
                                                  }
                                              }
                                    }
                                    name={maxValueName}
                                />
                            </Grid>
                        </>
                    ) : (
                        <Grid item xs={4.5}>
                            <Box>
                                <Input
                                    textFieldProps={
                                        isPercentage
                                            ? {
                                                  placeholder: PERCENT_PLACEHOLDER,
                                                  InputProps: {
                                                      inputComponent: PercentageFormat as any
                                                  }
                                              }
                                            : {
                                                  InputProps: {
                                                      inputComponent: NumericFormatCustom as any
                                                  }
                                              }
                                    }
                                    name={conditionValueName}
                                />
                            </Box>
                        </Grid>
                    )}
                </>
            )}
        </Grid>
    );
};

export default InequalitiesCondition;
