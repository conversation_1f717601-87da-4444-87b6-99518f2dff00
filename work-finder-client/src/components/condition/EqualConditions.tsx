import { Grid } from '@mui/material';

import { MultipleSelect, Select } from 'components/extended/Form';
import { EQUAL_CONDITION } from 'constants/Common';
import { IOption } from 'types';

type EqualConditionsProps = {
    conditionName: string;
    conditionValueName: string;
    conditionSelection: IOption[];
};

const EqualConditions = ({ conditionValueName, conditionName, conditionSelection }: EqualConditionsProps) => {
    return (
        <Grid container spacing={1}>
            <Grid item xs={3}>
                <Select name={conditionName} selects={EQUAL_CONDITION} />
            </Grid>
            <Grid item xs={4.5}>
                <MultipleSelect
                    name={conditionValueName}
                    selects={conditionSelection}
                    label={''}
                    isMultipleLanguage={false}
                    placeholder="select-option"
                />
            </Grid>
        </Grid>
    );
};

export default EqualConditions;
