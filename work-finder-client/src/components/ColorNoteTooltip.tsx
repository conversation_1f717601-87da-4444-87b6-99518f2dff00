import React from 'react';

import { Box, Grid, Tooltip, useTheme, Typography } from '@mui/material';
import { FormattedMessage } from 'react-intl';

import { IOption } from 'types';

type ColorNoteTooltipProps = {
    notes: IOption[];
    children: React.ReactNode;
    width?: string | number;
};

const ColorNoteTooltip = ({ notes, children, width }: ColorNoteTooltipProps) => {
    const theme = useTheme();
    return (
        <Tooltip
            slotProps={{
                tooltip: {
                    sx: {
                        backgroundColor: '#a6a6a6'
                    }
                }
            }}
            title={
                <Grid container sx={{ [theme.breakpoints.up('md')]: { width: width ? width : '120px' } }}>
                    {notes.map((type: IOption, index: number) => (
                        <Grid key={index} container spacing={1} sx={{ alignItems: 'center', padding: '5px' }}>
                            <Grid item>
                                <Box
                                    style={{
                                        backgroundColor: type.color,
                                        width: 15,
                                        height: 10,
                                        display: 'flex',
                                        justifyContent: 'center',
                                        alignItems: 'center',
                                        borderRadius: '4px'
                                    }}
                                ></Box>
                            </Grid>
                            <Grid item xs zeroMinWidth>
                                <Typography sx={{ color: 'white' }} align="left" variant="body2" fontSize={10}>
                                    <FormattedMessage id={type.label} />
                                </Typography>
                            </Grid>
                        </Grid>
                    ))}
                </Grid>
            }
            placement="top"
        >
            <Box>{children}</Box>
        </Tooltip>
    );
};

export default ColorNoteTooltip;
