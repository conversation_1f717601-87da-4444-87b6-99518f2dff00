import React, { useState, useEffect } from 'react';

// third-party
import { IntlProvider, MessageFormatElement } from 'react-intl';
import useConfig from 'hooks/useConfig';
import { Box, CircularProgress } from '@mui/material';
import sendRequest from 'services/ApiService';
import Api from 'constants/Api';
import { useAppDispatch } from 'app/hooks';
import { openSnackbar } from 'store/slice/snackbarSlice';

// load locales files
const loadLocaleData = () => {
    return import('utils/locales/en.json');
};

// ==============================|| LOCALIZATION ||============================== //

interface LocalsProps {
    children: React.ReactNode;
}

const Locales = ({ children }: LocalsProps) => {
    const { locale, onChangeLocale } = useConfig();

    const [messages, setMessages] = useState<Record<string, string> | Record<string, MessageFormatElement[]> | undefined>();
    const [loading, setLoading] = useState<boolean>(false);
    const dispatch = useAppDispatch();

    useEffect(() => {
        (async () => {
            try {
                setLoading(true);
                let messages = {};
                const res: any = await Promise.all([
                    loadLocaleData(),
                    sendRequest(Api.flexible_textConfig.configByLanguage, { language: locale })
                ]);
                if (res[0]) {
                    messages = { ...messages, ...(res[0].default || {}) };
                }
                if (res[1]?.status) {
                    messages = {
                        ...messages,
                        ...res[1].result.content?.reduce(
                            (prev: any, next: { key: any; value: any }) => ({ ...prev, [next.key]: next.value }),
                            {}
                        )
                    };
                }

                setMessages(messages);
            } catch (error: any) {
                dispatch(
                    openSnackbar({
                        open: true,
                        message: error?.message || 'error',
                        variant: 'alert',
                        alert: { color: 'error' }
                    })
                );
            } finally {
                setLoading(false);
            }
        })();
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [onChangeLocale, locale]);

    return (
        <>
            {loading ? (
                <Box
                    sx={{
                        display: 'flex',
                        justifyContent: 'center',
                        alignItems: 'center',
                        width: '100vw',
                        height: '100vh'
                    }}
                >
                    <CircularProgress />
                </Box>
            ) : (
                <>
                    {messages && (
                        <IntlProvider locale={locale} defaultLocale="en" messages={messages}>
                            {children}
                        </IntlProvider>
                    )}
                </>
            )}
        </>
    );
};

export default Locales;
