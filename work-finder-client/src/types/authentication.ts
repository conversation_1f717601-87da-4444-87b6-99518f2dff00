export interface Id {
    timestamp: number;
    date: Date;
}

export interface IUserInfo {
    id: Id;
    userId: string;
    userName: string;
    userType?: any;
    lastName: string;
    firstName: string;
    departmentId: string;
    onboardDate: Date;
    outboardDate?: any;
    created: Date;
    creator: string;
    lastUpdate: Date;
    userUpdate: string;
    status: string;
    titleCode: string;
    rankId: string;
    rankCost: number;
    allocationCost: number;
    memberCode: string;
    contractor: string;
    logtime: string;
    idHexString?: string;
    groups: string[];
    featureList: string[];
    functionList: string[];
    funtions?: any;
    userTitle: string;
    userRank: string;
    userDept: string;
    accountType?: string;
    businessId?: string;
    citizenId?: string;
    companyName?: string;
    email?: string;
    isLdap: boolean;
    password?: string;
    phone?: string;
    shortName?: string;
    taxCode?: string;
    role?: IRole[];
}

export interface IRole {
    id: string;
    idHexString: string;
    functions: string;
    groupId: string;
    groupName: string;
    note: string;
    groupType: string;
}

export interface ILoginResponse {
    content: {
        authen: boolean;
        token: { accessToken: string; tokenId: string };
    };
}

export interface IChangePasswordRequest {
    userId: string;
    passwordNow: string;
    newPassword: string;
    confirmPassword: string;
}

export interface ILoginRequest {
    username: string;
    password: string;
    ldap: boolean;
}

export interface IPackageAccount {
    id: string;
    name: string;
    price: number;
    description: string;
    expiresIn: string;
    unit: string;
    features: string[];
}

export interface IGetPackageAccountResponse {
    content: IPackageAccount[];
}

export interface IRegisterRequest {
    companyName: string;
    shortName: string;
    businessId: string;
    taxCode: string;
    firstName: string;
    lastName: string;
    citizenId: string;
    accountType: string;
    username: string;
    phone: string;
    email: string;
    packageAccountId: string;
}

export interface ICreatePasswordRequest {
    userId: string;
    token: string;
    password: string;
}

export interface ICreatePasswordResponse {
    content:
        | string
        | {
              message: string;
          };
}

export interface IVerifyEmailRequest {
    token: string;
    userId: string;
}

export interface IVerifyEmailResponse {
    content: {
        message: string;
        statusVerify: 'PENDING' | 'VERIFIED' | 'CREATED';
    };
}
