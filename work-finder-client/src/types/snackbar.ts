// material-ui
import { AlertProps, SnackbarOrigin } from '@mui/material';

// ==============================|| SNACKBAR TYPES  ||============================== //

export interface SnackbarProps {
    action: boolean;
    open: boolean;
    message: string;
    anchorOrigin: SnackbarOrigin;
    isMultipleLanguage: boolean;
    variant: string;
    alert: AlertProps;
    transition: string;
    close: boolean;
    actionButton: boolean;
    duration: number;
}
