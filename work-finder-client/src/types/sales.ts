// Monthly Production Performance
export interface IMonthlyProductionPerformance {
    id: string;
}
// ==============================|| Monthly Production Performance ||============================== //

// Monthly Production Performance List
export interface IDataByMonth {
    value: string;
    comment: string;
    projectId?: string;
}

export interface IDataHCByMonth {
    month: string;
    value: string;
}

export interface IDepartmentPerformanceDataByTotal {
    month: string;
    delivered: string;
    receivable: string;
    received: string;
    financial: string;
}

export interface IDepartmentPerformanceDataByMonth {
    month: string;
    delivered: IDataByMonth;
    receivable: IDataByMonth;
    received: IDataByMonth;
    financial: IDataByMonth;
}

export interface IDepartmentPerformanceData {
    idHexString: string;
    project: IDataByMonth;
    departmentId: string;
    contractSize: IDataByMonth;
    contractType: string;
    serviceType: string;
    months: IDepartmentPerformanceDataByMonth[];
}

export interface IDepartmentPerformance {
    name: string;
    departmentId: string;
    total: IDepartmentPerformanceDataByTotal[];
    data: IDepartmentPerformanceData[];
}

export interface IMonthlyProductionPerformanceInfo {
    months: string[];
    departments: IDepartmentPerformance[];
    companyTotals: {
        totalHCs: IDataHCByMonth[];
        productivity: number[];
        saleTotals: ISaleTotal[];
    };
}

export interface ISaleTotal {
    text: string;
    total: IDepartmentPerformanceDataByTotal[];
    index?: number;
    titleRecipe?: string;
    id?: string;
    style?: {
        fontWeight?: string;
        backgroundColor?: string;
        fontStyle: string;
        color: string;
        textDecoration: string;
    };
    flexibleStyle?: {
        fontWeight?: string;
        backgroundColor?: string;
        fontStyle: string;
        color: string;
        textDecoration: string;
    };
}

export interface IMonthlyProductionPerformanceResponse {
    content: IMonthlyProductionPerformanceInfo;
}

export interface IMonthlyProductionPerformanceDetailResponse {
    content: IDepartmentPerformanceDataByMonth;
}

export interface ICommentItem {
    type: string;
    idHexString: string;
    label: string;
    content: string;
    month?: string;
}

// Monthly Production Performance Add Or Edit
interface IObject {
    value?: number | null;
    label?: string;
    comment?: string;
    projectId?: number;
    projectName?: string;
}
export interface IProductivityHcInfo {
    role: string;
    rate: number | string | null;
    rateUSD: number | string | null;
    quantity: number | string | null;
    amount: any | string;
}

export interface IProductivity {
    month: number | null;
    delivered: IObject | null;
    receivable: IObject | null;
    received: IObject | null;
    financial: IObject | null;
    hcInfo: IProductivityHcInfo[];
}

//
export interface IHcInfos {
    role: string;
    rate: number;
    rateUSD: number;
    quantity: number;
    workDay: number;
    amount: number;
}
export interface IDurations {
    fromDate: string | null;
    toDate: string | null;
}

export interface IMonthlyProductionPerformanceAddOrEditForm {
    idHexString: string;
    year: number;
    month: number;
    projectId: any;
    departmentId: string;
    projectName: string;
    contractSize: string;
    serviceType: string;
    contractType: string;
    originalContractSize: string;
    unit: string;
    contractAllocation: string;
    duration: IDurations;
    paymentTerm: string;
    currency: string;
    standardWorkingDay?: string;
    exchangeRate?: number;
    delivered: number | undefined | null;
    receivable: number | undefined | null;
    received: number | undefined | null;
    financial: number | undefined | null;
    deliveredCurrency: number | undefined | null;
    receivableCurrency: number | undefined | null;
    receivedCurrency: number | undefined | null;
    financialCurrency: number | undefined | null;
    hcInfo: any;
    productivity?: IProductivity[];
    lastYearProductivity?: IProductivity[];
}

export interface ILastYearProductivity {
    month: number;
    delivered: IObjectEstimane;
    receivable: IObjectEstimane;
    received: IObjectEstimane;
    financial: IObjectEstimane;
    hcInfo: [];
    year: number;
}

export interface IObjectEstimane {
    value: number;
    comment: string;
}
export interface ICurrency {
    idHexString: string;
    year: number;
    currency: string;
    userCreate: string;
    userUpdate: string;
    dateCreate: string;
    lastUpdate: string;
    exchangeRate: number;
}

export interface IAddOrEditProductivityResponse {
    content: any;
}

export interface IEditCommentResponse {
    content: IDepartmentPerformanceData;
}

export interface IDeleteProductivityResponse {
    content: string;
}

// Edit HeadCount
export interface IProductivityHeadCountEditForm {
    year: number;
    month: number | string;
    value: string;
}

export interface IHeadCountValueByMonthDetailResponse {
    content: IDataHCByMonth;
}

// Comment
export interface ICommentForm {
    idHexString: string;
    month?: string;
    projectName?: string;
    contractSize?: string;
    delivered?: string;
    receivable?: string;
    received?: string;
    financial?: string;
}

//Sale Lead
//Requests Checking
export interface IIdRequestsChecking {
    timestamp: number;
    date: string;
}

export interface IRequestsChecking {
    id?: IIdRequestsChecking;
    partnerName: string;
    picFirstName?: string;
    picLastName?: string;
    status: string | null;
    receivedDate: Date | null | string;
    request: string;
    timeline: string;
    note: string;
    picUserName: any;
    domain: string;
    possibility: string;
    quantity: string;
    technology: string;
}

export interface IRequestsCheckingResponse {
    content: IRequestsChecking[];
}

//Supplier Checking
export interface IIdSupplierChecking {
    timestamp: number;
    date: string;
}

export interface ISupplierChecking {
    id?: IIdSupplierChecking;
    picFirstName?: string;
    picLastName?: string;
    picUserName: any;
    note: string;
    fromDate: Date | null | string;
    toDate: Date | null | string;
    creator?: string;
    supplierName: string;
    technology: string;
    unitPrice: string | null;
    userUpdate?: string;
    workType: string;
    createdDate?: string;
    lastUpdate?: string;
    quantity: string;
}

export interface ISupplierCheckingResponse {
    content: ISupplierChecking[];
}

// ==============================|| Sale pipeline ||============================== //

// On-Going
export interface IMonthlyHCList {
    month: number;
    hcMonthly: number | null;
    billableDay: number | null;
    billable: number | null;
}

export interface IProjectInfo {
    year: number | null;
    customer: string;
    contractType: string;
    projectName: string;
    contractNo: string;
    productionPerformanceIdHexString: string | null;
    serviceType: string;
    note: string;
    probability: string;
    status: string;
    revenuePercent: number | null;
    contractDueDate: string | null;
    contractDurationFrom: string | null;
    contractDurationTo: string | null;
    warrantyTime: string;
    type: string;
    projectNameComment?: string;
}

export interface IFinancialInfo {
    originalContractSize?: number | string;
    sizeVND: number | string;
    sizeUSD: number | string;
    managementRevenueAllocated: number | string;
    accountRevenueAllocatedVNDValue: number | string;
    newSaleUSD: number | string;
    currency: string;
    exchangeRate: number | string;
    acctReceivables: number | string;
    netEarn: number | string;
    paid: number | string;
    remain: number | string;
    quarterLicense1st: number | string;
    quarterLicense2nd: number | string;
    quarterLicense3rd: number | string;
    quarterLicense4th: number | string;
    licenseFee: number | string;
    accountantRevenueComment?: string;
    licenseFeeComment?: string;
    managementRevenueComment?: string;
    paidComment?: string;
    sizeVNDComment?: string;
}

export interface IHcInfo {
    billableHcs: number | string;
    hcs: number | string;
    teamLeadHcs: number | string;
    seniorHcs: number | string;
    middleHcs: number | string;
    juniorHcs: number | string;
    quarter1st: number | string;
    quarter2nd: number | string;
    quarter3rd: number | string;
    quarter4th: number | string;
    totalBillable: number | string;
    totalNewSale: number | string;
    monthlyHCList: IMonthlyHCList[];
}

export interface IOtherInfo {
    contact: {
        idHexString: string;
        firstName: string;
        lastName: string;
    } | null;
    presaleFolder: string;
    customerContact: string;
    phoneNumber: string | null;
    emailAddress: string;
}

export interface ISalesTotal {
    text: string;
    total: number | string;
    reportName?: string;
    id?: string;
    index: number;
    titleRecipe?: string;
    style?: {
        fontWeight?: string;
        backgroundColor?: string;
        fontStyle: string;
        color: string;
        textDecoration: string;
    };
    show?: boolean;
}

export interface ISaleOnGoingItem {
    idHexString: string;
    projectInfo: IProjectInfo;
    financialInfo: IFinancialInfo;
    hcInfo: IHcInfo;
    otherInfo: IOtherInfo;
}

export interface ISaleOnGoingList {
    content: { data: ISaleOnGoingItem[] };
}

export interface ISaleOnGoingTotals {
    content: { totals: ISalesTotal[] };
}
//Bidding
// Exchange rate
export interface ICurrencyBidding {
    currency: string;
    exchangeRate: number;
}

export interface ICurrencyBiddingList {
    content: ICurrencyBidding[];
}

export interface IHCMonthlyList {
    billable: number;
    billableDay: number;
    hcMonthly: number;
    month: number;
    year: number;
}

export interface ISaleBiddingItem {
    idHexString: string;
    serviceType: string;
    contractType: string;
    customer: string;
    projectName: string;
    contractNo: string;
    probability: string;
    status: string;
    sizeVND: string;
    sizeUSD: string;
    accountantRevenueAllocated: string;
    managementRevenueAllocated: string;
    paid: string;
    licenseFee: string;
    contractDueDate: string;
    contractFromDate: string;
    contractToDate: string;
    warrantyTime: string;
    projectNameComment: string;
    accountantRevenueComment: string;
    licenseFeeComment: string;
    managementRevenueComment: string;
    paidComment: string;
    sizeVNDComment: string;
    revenuePercent: string;
}

export interface ITotalBidding {
    text: string;
    total: number | string;
    reportName?: string;
    id?: string;
    index: number;
    titleRecipe?: string;
    style?: {
        fontWeight?: string;
        backgroundColor?: string;
        fontStyle: string;
        color: string;
        textDecoration: string;
    };
    show?: boolean;
}

export interface IBidding {
    dataList: ISaleBiddingItem[];
}

export interface IBiddingResponse {
    content: IBidding;
}

export interface IAllBidding {
    total: ITotalBidding[];
}

export interface ITotalBiddingResponse {
    content: IAllBidding;
}

export interface ICreateBiddingReportResponse {
    content: string;
}

export interface IBiddingProjectInfo {
    customer: string;
    projectName: string;
    projectRedmineId: string;
    projectRedmineName: string;
    type: string;
    serviceType: string;
    contractType: string;
    note: string;
    department: string;
    contractNo: string;
    probability: number;
    status: string;
    contractDueDate: string;
    contractDurationFrom: string;
    contractDurationTo: string;
    warrantyTime: string;
}

export interface IBiddingFinancialInfo {
    originalContractSize: number;
    sizeVND: number;
    sizeUSD: number;
    managementRevenueAllocated: number;
    accountRevenueAllocatedVND: number;
    newSaleUSD: number;
    currency: string;
    exchangeRate: number;
    acctReceivables: number;
    netEarn: number;
    paid: number;
    remain: number;
    quarterLicense1st: number;
    quarterLicense2nd: number;
    quarterLicense3rd: number;
    quarterLicense4th: number;
    licenseFee: number;
}

export interface IBiddingHcInfo {
    billableHcs: number;
    hcs: number;
    teamLeadHcs: number;
    seniorHcs: number;
    middleHcs: number;
    juniorHcs: number;
    quarter1st: number;
    quarter2nd: number;
    quarter3rd: number;
    quarter4th: number;
    totalBillable: number;
    totalNewSale: number;
    // TODO: Fix any
    fixCostHcInfo?: any;
    hcInfoMonth?: any;
}

export interface IBiddingOtherInfo {
    contact: {
        idHexString: string;
        firstName: string;
        lastName: string;
    };
    presaleFolder: string;
    customerContact: string;
    phoneNumber: string;
    emailAddress: string;
}

export interface IDetailBidding {
    idHexString?: string;
    project: IBiddingProjectInfo;
    financialInfo: IBiddingFinancialInfo;
    hcInfo: IBiddingHcInfo;
    otherInfo: IBiddingOtherInfo;
}

export interface IDetailBiddingResponse {
    content: IDetailBidding;
}

// Sale pipelime summary
export interface ISalePipelineSummary {
    tableLarge: ITableLarge;
    tableSmall: ITableSmall;
}

export interface ITableLarge {
    listParent: IListParent[];
}
export interface ITableSmall {
    listTable: IListTable[];
}

export interface IListParent {
    name: string;
    color?: string;
    backgroundColor?: string;
    value?: IValueSummary;
    childrens?: IListParent[];
}

export interface IListTable {
    name: string;
    color?: string;
    value?: number;
    valueUsd?: number;
    index?: number;
    titleRecipe?: string;
    id?: string;
    style?: {
        fontWeight?: string;
        backgroundColor?: string;
        fontStyle: string;
        color: string;
        textDecoration: string;
    };
    show?: boolean;
}
export interface IValueSummary {
    usd: number | null;
    vnd: number | null;
    quarter1: number | null;
    quarter2: number | null;
    quarter3: number | null;
    quarter4: number | null;
    totalRevenueEstimation: number | null;
}

// Budgeting plan
export interface IProjectInfoBudgetingPlan {
    year: string | number;
    salePipelineIdHexString: string;
    salePipelineType: string;
    projectName: string;
    type: string;
    serviceType: string;
    note: string;
    riskFactor: number;
    numberOfMonths: number;
    contractedValue: number;
    effortLimitManHours: number | null;
    costLimitVND: number | null;
    licenseFee: number | null;
}

export interface IProjectKPIScore {
    estimateUsedEffort: number;
    estimatedUseCost: number;
    planDelivery: number;
    totalOnTimeDelivery: number;
    effortKPIScore: number;
    costKPI: number;
    deadlineKPI: number;
    taskMGT: number;
    kpiScore: number;
}

export interface IProjectKPIBonus {
    addedEbitda: string;
    projectSetRevenue: number;
    actualCostByACD: number;
    companyRevActualCost: number;
    projectMGTPerformanceLevel: number;
    projectSavingCost: number;
    estimatedKPIProjectSavingCost: number;
    estimatedShareCompanyProfit: number;
    estimatedTotalKPIBonus: number;
    totalKPIBonus: number;
    kpiBonus: number;
}

export interface ITotalBudgetingPlan {
    text: string;
    total: number | string;
    reportName?: string;
    id?: string;
    index: number;
    titleRecipe?: string;
    style?: {
        fontWeight?: string;
        backgroundColor?: string;
        fontStyle: string;
        color: string;
        textDecoration: string;
    };
    show?: boolean;
}

export interface IBudgetingPlan {
    name: string;
    budgetPlan: IBudgetingPlanItem[];
}

export interface IBudgetingPlanItem {
    idHexString: string;
    projectInfo: IProjectInfoBudgetingPlan;
    projectKPIScore: IProjectKPIScore;
    projectKPIBonus: IProjectKPIBonus;
}

// Monitor Bidding Packages

// ======= Bidding Tracking
export interface ILocations {
    provCode: string;
    provName: string;
    districtCode: string;
    districtName: string;
}
export interface IBiddingTracking {
    id: string;
    type: string;
    khlcntNumber: string;
    packageName: string;
    postingDate: string;
    biddingClosingTime: string;
    biddingParticipationForm: string;
    tbmtNumber: string;
    group: string;
    company: string;
    address: string;
    keyword: string;
    link: string;
}

export interface IBiddingTrackingResponse {
    content: IBiddingTracking[];
}

export interface IBiddingReport {
    type: string;
    idHexString?: string;
    numberKHLCNT: string;
    biddingPackagesName: string;
    estimatedCost: number | null;
    datePosting: string;
    timeBiddingClosing: string;
    formBiddingParticipation: string;
    numberTBMT: string;
    group: string;
    link: string;
    address: string;
    status: string;
    company: string;
    keyword: string;
    bidPrice: number | null;
    comment: string;
}

export interface IBiddingReportResponse {
    content: IBiddingReport[];
}

export interface IBiddingDownloadParams {
    type?: string;
    biddingPackagesName?: string;
    address?: string;
    status?: string;
}
