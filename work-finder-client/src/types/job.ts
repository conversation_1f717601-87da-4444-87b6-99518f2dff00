// ================================|| JOB DOMAIN TYPES ||================================ //

// Base Job interfaces - extend from existing IFeaturedJob
export interface IJob {
    id: number;
    title: string;
    company: string;
    location: string;
    salary: string;
    type: string;
    tags?: string[];
    postedTime: string;
    logo?: string;
    companyLogo?: string;
    description?: string;
    requirements?: string[];
    benefits?: string[];
    applicationCount?: number;
    viewCount?: number;
    isActive?: boolean;
    expiryDate?: string;
    createdAt?: string;
    updatedAt?: string;
    employerId?: number;
}

// Extended Job with user interaction states
export interface IJobWithInteraction extends IJob {
    isBookmarked?: boolean;
    isFeatured?: boolean;
    isUrgent?: boolean;
    isApplied?: boolean;
    applicationStatus?: 'pending' | 'approved' | 'rejected' | 'reviewing';
}

// Job search and filter types
export interface IJobSearchFilters {
    keywords?: string;
    location?: string;
    category?: string;
    jobType?: string;
    experienceLevel?: string;
    salaryRange?: {
        min?: number;
        max?: number;
    };
    postedDate?: 'last24h' | 'last7days' | 'last30days' | 'all';
    companyType?: string[];
    tags?: string[];
}

// New dynamic filter system types
export interface IFilterOption {
    value: string;
    label: string;
}

export interface IFilterType {
    id: string;
    label: string;
    options: IFilterOption[];
}

export interface IDynamicFilter {
    filterType: string;
    filterValue: string;
}

export interface IDynamicFilterProps {
    selectedFilter: IDynamicFilter;
    onFilterChange: (filter: IDynamicFilter) => void;
    filterTypes: IFilterType[];
}

// Job-specific pagination interfaces (using common pagination types)
import { IPaginationResponse } from './common';

export interface IJobPaginationData {
    total: number;
    page: number;
    size: number;
}

export interface IJobsResponse {
    jobs: IJobWithInteraction[];
    pagination: IPaginationResponse;
}

export interface IJobSearchRequest {
    filters: IJobSearchFilters;
    page?: number;
    limit?: number;
    sortBy?: 'relevance' | 'date' | 'salary' | 'company';
    sortOrder?: 'asc' | 'desc';
}

export interface IJobSearchResponse {
    content: {
        jobs: IJobWithInteraction[];
        totalCount: number;
        totalPages: number;
        currentPage: number;
        hasNext: boolean;
        hasPrevious: boolean;
    };
}

// Job Category types
export interface IJobCategory {
    id: number;
    title: string;
    openPositions: number;
    icon: string;
    color?: string;
    description?: string;
}

// Job application types
export interface IJobApplication {
    id: number;
    jobId: number;
    candidateId: number;
    status: 'pending' | 'approved' | 'rejected' | 'reviewing';
    appliedDate: string;
    coverLetter?: string;
    resumeUrl?: string;
    notes?: string;
}

export interface IJobApplicationRequest {
    jobId: number;
    coverLetter?: string;
    resumeFile?: File;
}

export interface IJobApplicationResponse {
    content: {
        applicationId: number;
        message: string;
        status: string;
    };
}

// Bookmark types
export interface IJobBookmark {
    id: number;
    jobId: number;
    candidateId: number;
    bookmarkedDate: string;
}

export interface IBookmarkJobRequest {
    jobId: number;
}

export interface IBookmarkJobResponse {
    content: {
        bookmarkId?: number;
        message: string;
        isBookmarked: boolean;
    };
}

// Component Props types for containers
export interface IJobItemProps {
    job: IJobWithInteraction;
    variant?: 'default' | 'compact' | 'featured' | 'list';
    showBookmark?: boolean;
    showTags?: boolean;
    showDetails?: boolean;
    showApplicationButton?: boolean;
    onJobClick?: (job: IJobWithInteraction) => void;
    onBookmarkClick?: (job: IJobWithInteraction, event: React.MouseEvent) => void;
    onApplyClick?: (job: IJobWithInteraction) => void;
    sx?: object;
}

export interface IJobListProps {
    jobs: IJobWithInteraction[];
    loading?: boolean;
    variant?: 'grid' | 'list';
    itemVariant?: 'default' | 'compact' | 'featured';
    showLoadMore?: boolean;
    showFilters?: boolean;
    onLoadMore?: () => void;
    onJobClick?: (job: IJobWithInteraction) => void;
    onBookmarkClick?: (job: IJobWithInteraction, event: React.MouseEvent) => void;
    onApplyClick?: (job: IJobWithInteraction) => void;
    onFiltersChange?: (filters: IJobSearchFilters) => void;
}

// Job detail types
export interface IJobDetail extends IJobWithInteraction {
    companyInfo: {
        id: number;
        name: string;
        description: string;
        size: string;
        industry: string;
        website?: string;
        logo?: string;
        address?: string;
    };
    similarJobs?: IJob[];
}

export interface IJobDetailRequest {
    jobId: number;
}

export interface IJobDetailResponse {
    content: IJobDetail;
}

// Homepage specific types - compatibility with existing code
export interface IFeaturedJob extends IJob {
    // For backward compatibility with existing IFeaturedJob
}

export interface IHomepageSearchConfig {
    keywords: string;
    location: string;
    category: string;
}
