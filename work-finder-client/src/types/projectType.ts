export interface IProjectType {
    id: string;
    billable: string;
    created: Date;
    userCreator: string;
    projectTypeName: string;
    colorCode: string;
    userUpdate: string;
    lastUpdate: Date;
    typeCode: string;
    fixCost: boolean;
}

export interface IGetProjectTypeResponse {
    content: IProjectType[];
}

export interface ICreateProjectTypeRequest {
    typeCode: string;
    projectTypeName: string;
    billable: string;
    colorCode: string;
    fixCost: boolean;
}

export interface IEditProjectTypeRequest {
    id: string;
    typeCode: string;
    projectTypeName: string;
    billable: string;
    colorCode: string;
    fixCost: boolean;
}
